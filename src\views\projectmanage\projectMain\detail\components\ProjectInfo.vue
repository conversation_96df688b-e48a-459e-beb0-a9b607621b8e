<template>
  <div id="project-info" class="section-block">

    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <h2 style="margin: 0;">项目组信息</h2>

<!--      <div style="display: flex; gap: 20px; align-items: center;">-->
<!--        <div style="display: flex; align-items: center; gap: 8px;">-->
<!--          <span style="font-weight: normal; color: #666;">性质:</span>-->
<!--          <dict-tag :type="'xmzxz'" :value="projectData.nature" v-if="projectData.nature" />-->
<!--          <span v-else style="color: #999;">-</span>-->
<!--        </div>-->
<!--        <div style="display: flex; align-items: center; gap: 8px;">-->
<!--          <span style="font-weight: normal; color: #666;">重要性:</span>-->
<!--          <dict-tag :type="'xmzzyx'" :value="projectData.importance" v-if="projectData.importance" />-->
<!--          <span v-else style="color: #999;">-</span>-->
<!--        </div>-->
<!--        <div style="display: flex; align-items: center; gap: 8px;">-->
<!--          <span style="font-weight: normal; color: #666;">项目系数:</span>-->
<!--          <span style="font-weight: 500;">{{ projectData.coefficient || '-' }}</span>-->
<!--        </div>-->
<!--      </div>-->
    </div>
    <el-descriptions :column="2" border :data="projectData"  v-loading="loading">
      <el-descriptions-item label="项目组名称" prop="projectGropName" width="60" >{{projectData.projectGropName }}</el-descriptions-item>
      <el-descriptions-item label="项目组编号" prop="projectGropNumber"   width="60" >{{projectData.projectGropNumber }}</el-descriptions-item>
<!--      <el-descriptions-item label="开始日期">{{projectData.startDate || '2024-06-01'}}</el-descriptions-item>-->
<!--      <el-descriptions-item label="计划结束日期">{{projectData.endDate || '2024-12-31'}}</el-descriptions-item>-->
      <el-descriptions-item label="项目状态"  width="60">
        <el-tag>{{ projectData.statusText }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="项目组类型" prop="projectManager" width="60" >   {{
          Array.isArray(projectData.type)
            ? projectData.type.join(',')
            : (projectData.type || '无')
        }}</el-descriptions-item>
      <el-descriptions-item
        label="项目经理"
        prop="projectManager"
        width="60"
      >
        <div @dblclick="handleEditProjectManager">
          {{ projectData.projectManager ? projectData.projectManager : '无' }}
        </div>
<!--        {{projectData.projectManager || '无'}}-->
      </el-descriptions-item>
      <el-descriptions-item
        label="项目组进度"
        prop="projectManager"  width="60">
        <div @dblclick="handlePercentageDblClick">
          {{ projectData.percentage ? `${projectData.percentage}%` : '无' }}
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-button
      @click="openApprovalForm('create')"
      type="primary"
      style="margin-top: 10px"
      v-if="hasEditPermission"
    >
      任命项目经理（OA流程）
    </el-button>

    <div style="margin-top: 10px">
      <h4 style="margin-bottom: 10px">项目经理审批附件</h4>
    <el-row v-for="group in groupFileList" :key="group[0].fileBusinessTypeDetail">
      <el-col :span="24">
        <!--            <el-card>-->
        <div>
          <span>{{ group[0].fileBusinessTypeDetail }}</span>
        </div>
        <!--group是个数组，每个对象里面有个url,  获取所有的url, 然后v-modle=所有的url,modelValue是url数组 -->
        <UploadFile
          :modelValue="collectedUrls(group)"
          :businessId="props.id"
          :fileBusinessType="`项目经理审批`"
          :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
          :url2Name="collectUrl2NameMap(group)"
          :businessFileList="group"
        />
        <!--            </el-card>-->
      </el-col>
    </el-row>
    </div>

    <div id="project-items" class="project-items-section">
        <!--      <template #header>-->
        <!--        <div class="card-header">-->
        <!--          <span>项目条目</span>-->
        <!--        </div>-->
        <!--      </template>-->
        <h3>项目条目</h3>

      <el-button
        link
        type="primary"
        @click="downloadTable()"
      >
        系数表
      </el-button>
      <div style="margin-top: 10px; display: flex; align-items: center;">
          <template v-if="hasEditPermission">
            <el-tooltip
              :content="hasPayableProject ? '不能选择付款项目进行关联，请取消勾选付款项目' : (selectedProjects.length === 0 || !hasReceivableProject ? '请先勾选收款项目' : '')"
              placement="top"
              v-if="selectedProjects.length === 0 || !hasReceivableProject || hasPayableProject"
            >
              <el-button
                type="primary"
                plain
                :disabled="selectedProjects.length === 0 || !hasReceivableProject || hasPayableProject"
                @click="openForm('create', 'payable')"
              >
                <Icon icon="ep:plus" class="mr-5px"/>
                新增付款项目
              </el-button>
            </el-tooltip>
            <el-button
              v-else
              type="primary"
              plain
              @click="openForm('create', 'payable')"
            >
              <Icon icon="ep:plus" class="mr-5px"/>
              新增付款项目
            </el-button>
          </template>
        </div>
        <!-- 项目列表 -->
        <el-table v-loading="itemsLoading" ref="tableRef" :data="projectList" :stripe="true"  @row-click="handleRowClick" highlight-current-row @selection-change="handleSelectionChange"  :show-overflow-tooltip="true">
          <el-table-column type="selection" width="55">
            <template #header>
              <el-tooltip content="可选择多个收款项目，不能选择付款项目" placement="top">
                <span>选择</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="编号" align="center" width="110" prop="projectCode"/>
<!--          <el-table-column label="名称" align="center" prop="projectName" width="150"/>-->
          <el-table-column label="名称" align="center" prop="projectName" width="150" >
            <template #default="scope">
              <span class="project-name-cell">{{ scope.row.projectName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合作单位" align="center" width="160">
            <template #default="{ row }">
              <el-tag v-if="row.payReciRelation=='收'" size="large">
                {{ row.payerName }}
              </el-tag>
              <el-tag v-if="row.payReciRelation=='付'" size="large">
                {{ row.payeeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="项目金额" align="center">
            <template #default="scope">
              <span>￥{{ formatMoney(scope.row.totalAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合作类型" align="center" width="80" >
            <template #default="{ row }">
              <el-tag :type="row.payReciRelation === '收' ? 'danger' : 'success'" size="large">
                {{ row.payReciRelation }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="验收情况" align="center"  width="80" >
            <template #default="{ row }">
              <el-tag  v-if="row.ifAccepted" :type="row.ifAccepted === '是' ? 'success' : 'danger'" size="large">
                <el-icon v-if="row.ifAccepted === '是'"><Check/></el-icon>
                <el-icon v-if="row.ifAccepted === '否'"><CloseBold/></el-icon>
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="来源/采购方式" align="center">
            <template #default="{ row }">
              {{ row.payReciRelation === '收' ? '直接委托' : row.buyMethods }}
            </template>
          </el-table-column>
          <el-table-column label="项目类别" align="center" prop="type" width="80"/>
          <el-table-column label="负责人" align="center" prop="projectManagerName" width="80"/>
          <el-table-column label="审批状态" align="center" width="80" prop="approvalStatus"  >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="oarouter(scope.row.flowId)"
            >
              {{ scope.row.approvalStatus }}
            </el-button>
          </template>
          </el-table-column>
          <el-table-column label="项目进度" align="center" width="80">
            <template #default="{ row }">
              {{ row.percentage !== undefined && row.percentage !== null ? row.percentage + '%' : '' }}
            </template>
          </el-table-column>
          <el-table-column label=" 重要性" align="center" prop="importance" width="90">
            <template #default="scope">
              <span>{{ scope.row.importance || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="性质" align="center" width="82">
            <template #default="scope">
              <span>{{ scope.row.nature || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目系数" align="center" width="80">
            <template #default="scope">
              <span>{{ scope.row.coefficient || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" align="center" width="82">
            <template #default="scope">
              <span>{{ scope.row.projectType || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" min-width="110px" v-if="hasEditPermission" >
            <template #default="scope">
              <div style="display: flex; flex-direction: column; gap: 4px; align-items: center">
              <el-button
                link
                type="primary"
                @click="handleEditProgress(scope.row)"
                style="width: 100%; justify-content: center"
              >
                编辑
              </el-button>
              <el-button
                link
                type="primary"
                v-if="scope.row.payReciRelation === '付' && projectData.projectManager && projectData.projectManager !== '无'"
                @click="openProjectApprovalForm(scope.row)"
                style="width: 100%; justify-content: center"
              >
                推送项目审批
              </el-button>
              </div>
<!--              <el-button-->
<!--                link-->
<!--                type="primary"-->
<!--                @click="handleViewDetail(scope.row)"-->
<!--              >-->
<!--                详情-->
<!--              </el-button>-->
<!--              <el-button-->
<!--                link-->
<!--                type="primary"-->
<!--                @click="handleViewContract(scope.row)"-->
<!--              >-->
<!--                合同-->
<!--              </el-button>-->
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="fetchProjects"
        />
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="修改项目组进度"
      width="30%">
      <el-form :model="form" @submit.prevent>
        <el-form-item label="项目组进度">
          <el-input
            placeholder="请输入项目进度"
            v-model="form.percentage"
            min="0"
            max="100"
            @input="handleInput"
            type="number"
            style="width: 20%">
          <template #suffix>
            <span>%</span>
          </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPercentage">确认</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="editProjectDialogVisible"
      title="编辑项目信息"
      width="50%">
      <el-form  ref="projectFormRef"  :model="projectForm" @submit.prevent label-width="100px"   :rules="formRules1">
        <el-form-item label="项目进度">
          <el-input
            placeholder="请输入项目进度"
            v-model="projectForm.percentage"
            min="0"
            max="100"
            @input="handleProjectInput"
            type="number"     style="width: 120px"
          >
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="项目负责人" prop="manager">
          <el-select  v-model="projectForm.projectManagerName" placeholder="请选择项目负责人" clearable filterable style="width: 200px">
            <el-option
              v-for="dict in getFilteredDictOptions()"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-divider content-position="left">项目组信息</el-divider>
        <el-card style="display: flex">
          <el-row>
            <el-col :span="12">
              <el-form-item label="项目组编号" prop="projectGroupCode">
                <el-input
                  v-model="projectForm.projectGroupCode" readonly
                  placeholder="请输入项目组编号"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="项目组名称" prop="projectGroupName" style="margin-right: 10px"
                @change="getProjectName">
                <el-input
                  v-model="projectForm.projectGroupName" readonly
                  placeholder="请输入项目组名称"/>
              </el-form-item>
            </el-col>
            <!--          <el-col :span="9">-->
            <!--            <el-button @click="bindProjectGroup" type="primary"  plain :disabled="formLoading" style="margin-left: 5px">绑定项目组</el-button>-->
            <!--            <el-button @click="generatedProjectGroupCode" type="success" plain :disabled="formLoading">为当前项目生成项目组</el-button>-->
            <!--          </el-col>-->
          </el-row>
        </el-card>
        <el-divider content-position="left" v-if="projectForm.payReciRelation==`收`">收款项目信息
        </el-divider>
        <el-divider content-position="left" v-else>付款项目信息</el-divider>
        <el-card style="display: flex">
          <el-row>
            <el-col :span="8">
              <el-form-item label="项目编号" prop="projectCode">
                <el-input v-model="projectForm.projectCode" placeholder="自动生成" readonly/>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="projectForm.projectName" type="text" placeholder="请输入项目名称"/>
              </el-form-item>
            </el-col>
          </el-row>



          <!--收款项目才显示-->
          <el-row v-if="projectForm.payReciRelation==`收`">
            <el-col :span="8">
              <!--            <el-form-item label="项目来源" prop="projectSourceUnitName">-->
              <!--              <el-input v-model="formData.projectSourceUnitName"-->
              <!--                        placeholder="请输入项目来源单位名"/>-->
              <!--            </el-form-item>-->

              <el-form-item label="项目来源" prop="projectSourceUnitName">
                <el-select
                  @change="projectSourceUnitNameChange"
                  v-model="projectForm.projectSourceUnitName"
                  filterable
                  placeholder="项目来源"
                  clearable
                  :loading="loading"
                >
                  <el-option
                    v-for="option in nameOptions"
                    :key="option.name"
                    :label="option.name"
                    :value="option.name"
                  />
                </el-select>
              </el-form-item>

            </el-col>
            <el-col :span="8">
              <el-form-item  label="联&nbsp; 系 &nbsp;人" prop="projectSourceUnitPerson" >
                <el-input
                  v-model="projectForm.projectSourceUnitPerson"
                  placeholder="请输入项目来源联系人"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系方式" prop="projectSourceUnitContactDetails">
                <el-input
                  v-model="projectForm.projectSourceUnitContactDetails"
                  placeholder="请输入联系方式"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="主办部门" prop="managingDepartment">
                <!--<el-input v-model="formData.managingDepartment" placeholder="请输入主管部门" />-->
                <template #label>
                  主办部门
                </template>
                <el-select
                  v-model="projectForm.managingDepartment" placeholder="请选择主办部门"
                  style="min-width: 150px" clearable @change="filteredCooperatingDepartment">
                  <el-option
                    v-for="dict in filteredManagingDepartmentList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="协办部门" prop="cooperatingDepartment">
                <!--<el-input v-model="formData.cooperatingDepartment" placeholder="请输入配合部门" />-->
                <el-select
                  v-model="projectForm.cooperatingDepartment" placeholder="请选择协办部门"
                  style="min-width: 150px" clearable @change="filteredManagingDepartment"  multiple>
                  <el-option
                    v-for="dict in filteredCooperatingDepartmentList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目类别" prop="type" >
                <el-select
                  v-model="projectForm.type" placeholder="项目类别"
                  style="min-width: 150px" clearable>
                  <el-option
                    v-for="dict in getStrDictOptions(`project_type`)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" >
              <el-form-item label="项目状态" prop="state" >
                <el-input v-model="projectForm.state" readonly/>
              </el-form-item>
            </el-col>

            <!-- 项目性质：只有收款项目才显示 -->
            <el-col :span="8" v-if="projectForm.payReciRelation === '收'">
              <el-form-item label="项目性质" prop="nature" v-if="hasEditPermission">
                <el-select
                  v-model="projectForm.nature"
                  placeholder="请选择项目性质"
                  style="min-width: 150px"
                  clearable
                  :disabled="!isDepartmentManager"
                >
                  <el-option
                    v-for="dict in getStrDictOptions('xmzxz')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 项目重要性：只有收款项目才显示 -->
            <el-col :span="8" v-if="projectForm.payReciRelation === '收'">
              <el-form-item label="项目重要性" prop="importance" v-if="hasEditPermission">
                <el-select
                  v-model="projectForm.importance"
                  placeholder="请选择项目重要性"
                  style="min-width: 150px"
                  clearable
                  :disabled="!isDepartmentManager"
                >
                  <el-option
                    v-for="dict in getStrDictOptions('xmzzyx')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 项目类型：只有收款项目才显示 -->
            <el-col :span="8" v-if="projectForm.payReciRelation === '收'">
              <el-form-item label="项目类型" prop="projectType" v-if="hasEditPermission">
                <el-select
                  v-model="projectForm.projectType"
                  placeholder="请选择项目类型"
                  style="min-width: 150px"
                  clearable
                  :disabled="!isDepartmentManager"
                >
                  <el-option
                    v-for="dict in getStrDictOptions('groupType')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 项目系数：只有收款项目才显示 -->
            <el-col :span="12" v-if="projectForm.payReciRelation === '收'">
              <el-form-item label="项目系数" prop="coefficient" class="w-95%" v-if="hasEditPermission">
                <div style="display: flex; gap: 10px; align-items: center;">
                  <el-input-number
                    v-model="projectForm.coefficient"
                    :min="0"
                    :precision="3"
                    style="width: 200px;"
                    placeholder="请输入项目系数"
                    :disabled="!isDepartmentManager"
                  />
                  <!-- 系数计算按钮只在编辑模式下且用户为部门经理时显示 -->
                  <el-button
                    v-if="editProjectDialogVisible && isDepartmentManager"
                    :type="coefficientError ? 'danger' : 'primary'"
                    size="default"
                    @click="handleCoefficientClick"
                    :loading="calculatingCoefficient"
                    style="white-space: nowrap; min-width: 140px;"
                  >
                    {{ getButtonTextShort() }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-form-item  prop="attribute"  >
              <el-radio-group v-model="projectForm.attribute">
                <el-radio label="零星">零星</el-radio>
                <el-radio label="非零星">非零星</el-radio>
              </el-radio-group>
            </el-form-item>
            <!--          <el-col :span="8">-->
            <!--            <el-form-item label="业务部项目编号" prop="busiDepProjectCode" style="min-width: 120px" label-width="150px" clearable>-->
            <!--              <el-input v-model="formData.busiDepProjectCode" placeholder="请输入业务部编号" />-->
            <!--            </el-form-item>-->
            <!--          </el-col>-->
          </el-row>
          <el-form-item label="项目内容" prop="projectContent">
            <el-input
              v-model="projectForm.projectContent" type="textarea" placeholder="请输入项目内容"
              maxlength="1000" show-word-limit/>
          </el-form-item>
        </el-card>

        <el-divider content-position="left">其他信息</el-divider>
        <el-card>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                type="textarea" v-model="projectForm.remarks" placeholder="请输入备注"
                maxlength="1000" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-card>

      </el-form>
      <template #footer>
    <span class="dialog-footer">
      <el-button @click="editProjectDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitProjectPercentage">确认</el-button>
    </span>
      </template>
    </el-dialog>

    <el-dialog title="新增项目经理审批" v-model="approvalDialogVisible" width="55%">
      <el-form
        ref="formManagerRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        v-loading="formLoading"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="formData.projectName" placeholder="请输入项目名称"/>
        </el-form-item>
        <el-form-item label="项目经理" prop="projectManager">
          <el-select v-model="formData.projectManager" placeholder="请选择项目经理" clearable  filterable >
            <el-option
              v-for="dict in getFilteredDictOptions()"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目简介" prop="projectIndroduction">
          <el-input
            v-model="formData.projectIndroduction"
            placeholder="请输入项目简介"
            :rows="8"
            type="textarea"
            :maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-row v-for="group in groupFileList" :key="group[0].fileBusinessTypeDetail">
          <el-col :span="24">
            <!--            <el-card>-->
            <div>
              <span>{{ group[0].fileBusinessTypeDetail }}</span>
            </div>
            <!--group是个数组，每个对象里面有个url,  获取所有的url, 然后v-modle=所有的url,modelValue是url数组 -->
            <UploadFile
:modelValue="collectedUrls(group)"
                        :businessId="props.id"
                        :fileBusinessType="`项目经理审批`"
                        :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
                        :url2Name="collectUrl2NameMap(group)"
                        :businessFileList="group"
            />
            <!--            </el-card>-->
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="debouncedSubmitForm" type="primary" >推 送</el-button>
        <el-button @click="approvalDialogVisible = false">取 消</el-button>
      </template>
    </el-dialog>

    <el-dialog title="新增项目审批" v-model="projectDialogVisible">
      <el-form
        ref="formProjectRef"
        :model="formProjectData"
        :rules="formProjectRules"
        label-width="135px"
        v-loading="formProjectLoading"
      >
        <el-form-item label="主项目名称" prop="mainProjectName">
          <el-input v-model="formProjectData.mainProjectName" placeholder="请输入主项目名称" />
        </el-form-item>
        <el-form-item label="子项目名称" prop="childProjectName">
          <el-input v-model="formProjectData.childProjectName" placeholder="请输入子项目名称" />
        </el-form-item>
        <el-form-item label="项目负责人" prop="projectManager">
<!--          <el-input v-model="formProjectData.projectManager" placeholder="请输入项目负责人"-->
<!--          >-->
<!--          </el-input>-->
          <el-select v-model="formProjectData.projectManager" placeholder="请选择项目负责人" clearable  filterable >
            <el-option
              v-for="dict in getFilteredDictOptions()"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目类别" prop="projectCatagory">
          <el-select v-model="formProjectData.projectCatagory" placeholder="请输入项目类别" clearable>
            <el-option
              v-for="dict in getStrDictOptions(`project_type`)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="formProjectData.phone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="预算金额（元）" prop="estimateMoney">
          <el-input v-model="formProjectData.estimateMoney" placeholder="请输入预算金额" />
        </el-form-item>
        <el-form-item label="预计工期" prop="estimateTime">

          <el-input v-model="formProjectData.estimateTime" placeholder="请输入预计工期" />
        </el-form-item>
        <el-form-item label="资金来源" prop="source">
          <el-input v-model="formProjectData.source" placeholder="请输入资金来源" />
        </el-form-item>
        <el-form-item label="是否涉及现场施工" prop="isSiteConstruction">
          <el-select v-model="formProjectData.isSiteConstruction" placeholder="请选择是否涉及现场施工" clearable>
            <el-option label="是" value="1" />
            <el-option label="否" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目简介" prop="projectIndroduction">
          <el-input
v-model="formProjectData.projectIndroduction"
                    placeholder="请输入项目简介"
                    :rows="8"
                    type="textarea"
                    :maxlength="255"
                    show-word-limit
          />
        </el-form-item>

        <el-row v-for="group in groupFileList2" :key="group[0].fileBusinessTypeDetail">
          <el-col :span="24">
            <!--            <el-card>-->
            <div>
              <span>{{ group[0].fileBusinessTypeDetail }}</span>
            </div>
            <!--group是个数组，每个对象里面有个url,  获取所有的url, 然后v-modle=所有的url,modelValue是url数组 -->
            <UploadFile
:modelValue="collectedUrls(group)"
                        :businessId="currentProject.id"
                        :fileBusinessType="`项目审批`"
                        :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
                        :url2Name="collectUrl2NameMap(group)"
                        :businessFileList="group"
            />
            <!--            </el-card>-->
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="debouncedSubmitProjectForm" type="primary" >确 定</el-button>
        <el-button @click="projectDialogVisible = false">取 消</el-button>
      </template>
    </el-dialog>

    <el-dialog v-model="editProjectManagerDialogVisible" title="修改项目经理" width="30%">
      <el-form :model="projectManagerForm" label-width="80px">
        <el-form-item label="项目经理">
          <el-select v-model="projectManagerForm.name" placeholder="请输入项目经理名称"  clearable filterable :disabled="projectData.approved === '1'">
          <el-option
            v-for="dict in getFilteredDictOptions()"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
    <span class="dialog-footer">
      <el-button @click="editProjectManagerDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveProjectManager">保存</el-button>
    </span>
      </template>
    </el-dialog>
    <el-dialog   align-center :title="previewDialogTitle" v-model="showPreviewDialog"  width="80%" >
      <DocxPreview  :file-blob="previewFileBlob" />

    </el-dialog>
    <!-- 添加项目表单组件 -->
    <ProjectForm ref="formRef" @success="handleSuccess"/>

  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, watch, defineProps, defineEmits, computed} from 'vue'
import {ProjectGropApi} from '@/api/projectmanage/projectgrop'
import { ProjectApi, ProjectVO } from '@/api/projectmanage/project'
import { useRouter, useRoute } from 'vue-router'
import emitter from '@/utils/eventBus'
import { useUserStoreWithOut } from '@/store/modules/user'
import { checkRole } from '@/utils/permission'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { Icon } from '@/components/Icon'
import { useMessage } from '@/hooks/web/useMessage'
import { CustomerApi } from '@/api/projectmanage/customer'
const { wsCache } = useCache()
const userStore = useUserStoreWithOut()
import ProjectManagerApprovalForm
  from "@/views/projectmanage/project/projectperson/ProjectManagerApprovalForm.vue";
import UploadFile from "../../../../../components/UploadFile/src/UploadFile.vue";
import {BusinessFileTypeApi} from "@/api/infraInfo/bussinessFileType";
import {
  ProjectManagerApprovalApi,
  ProjectManagerApprovalVO
} from '@/api/projectmanage/projectmanagerapproval'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";
import {pinyin} from "pinyin";
import {ElMessage, ElMessageBox, ElOption, ElSelect} from "element-plus";
import {ProjectApprovalApi} from "@/api/projectmanage/projectapproval";
import { debounce } from 'lodash-es'
import {Check, Close, CloseBold} from "@element-plus/icons-vue";
import ProjectForm from '@/views/projectmanage/project/ProjectForm.vue'
const previewDialogTitle = ref('')
const previewFileInfo=  ref();
const previewFileBlob=  ref();
const calculatingCoefficient = ref(false) // 计算系数的加载状态
const calculatedCoefficient = ref<number | null>(null) // 计算出的系数结果，初始为null
const coefficientError = ref(false) // 系数计算是否出错
const showPreviewDialog = ref(false);//预览对话框
const isDepartmentManager = ref(false) // 当前用户是否为部门经理

// 获取按钮文字（简短版）
const getButtonTextShort = () => {
  if (coefficientError.value) {
    return '参数缺失，无法计算系数'
  }
  if (calculatedCoefficient.value !== null) {
    if (calculatedCoefficient.value > 0) {
      return `按照系数表计算系数为:${calculatedCoefficient.value.toFixed(3)}`
    } else if (calculatedCoefficient.value === 0) {
      return '按照系数表计算系数为:0.000'
    }
  }
  return '按照系数表计算系数'
}

// 处理按钮点击事件
const handleCoefficientClick = async () => {
  // 如果已有计算结果，则填入项目系数字段
  if (calculatedCoefficient.value !== null) {
    if (calculatedCoefficient.value > 0) {
      projectForm.coefficient = calculatedCoefficient.value
      message.success(`已将系数 ${calculatedCoefficient.value.toFixed(3)} 填入项目系数字段`)
    } else if (calculatedCoefficient.value === 0) {
      projectForm.coefficient = 0
      message.success('已将系数 0 填入项目系数字段')
    }
  } else {
    // 否则进行计算
    await calculateCoefficient(true)
  }
}

// 自动计算项目系数
const calculateCoefficient = async (isUserClick = false) => {
  // 重置错误状态
  coefficientError.value = false

  // 检查是否为收款项目
  if (projectForm.payReciRelation !== '收') {
    coefficientError.value = true
    return
  }

  // 检查用户是否为部门经理
  if (!isDepartmentManager.value) {
    coefficientError.value = true
    return
  }

  // 检查必要参数是否完整
  if (!projectForm.nature) {
    coefficientError.value = true
    return
  }
  if (!projectForm.importance) {
    coefficientError.value = true
    return
  }
  if (!projectForm.projectType) {
    coefficientError.value = true
    return
  }

  try {
    calculatingCoefficient.value = true

    // 根据主办部门名称获取部门ID
    const getDeptIdByName = (deptName: string): number | null => {
      const deptMapping = {
        '软件开发一部': 199,
        '软件开发二部': 201,
        '系统集成部': 203
      }
      return deptMapping[deptName] || null
    }

    const deptId = getDeptIdByName(projectForm.managingDepartment)

    if (!deptId) {
      message.warning('无法获取项目主办部门信息，请先设置主办部门后再计算系数')
      coefficientError.value = true
      return
    }

    // 获取合同信息中的金额作为项目规模
    let contractAmount: number | undefined = undefined

    // 从项目的合同金额字段获取金额
    if (projectForm.totalAmount && projectForm.totalAmount > 0) {
      contractAmount = projectForm.totalAmount
    }

    if (!contractAmount || contractAmount <= 0) {
      message.warning('当前项目的合同金额为空或无效，无法计算系数')
      coefficientError.value = true
      return
    }

    const coefficient = await ProjectGropApi.calculateProjectCoefficient({
      deptId: deptId,
      nature: projectForm.nature,
      importance: projectForm.importance,
      receivable: contractAmount,
      type: projectForm.projectType ? [projectForm.projectType] : []
    })

    // 将计算结果填入计算结果字段
    calculatedCoefficient.value = coefficient

    if (coefficient > 0) {
      // message.success(`按系数表计算系数为：${coefficient}`)
    } else {
      message.info('当前条件下项目系数为0，请检查部门和参数设置')
    }
  } catch (error) {
    // console.error('计算项目系数失败:', error)
    coefficientError.value = true
    calculatedCoefficient.value = null
  } finally {
    calculatingCoefficient.value = false
  }
}

const downloadTable = () => {
  const fileUrl = 'http://10.200.17.131:9000/projectmanagebucket/043abeb0487e1079d74d9805376b873230604418107931d095abf397d24900fd.docx'
  //const fileUrl = 'http://127.0.0.1:58080/admin-api/infra/file/4/get/043abeb0487e1079d74d9805376b873230604418107931d095abf397d24900fd.docx'
  previewDialogTitle.value='系数表'
  const transformedUrl = transformUrl(fileUrl)

  // 创建XMLHttpRequest对象获取文件
  const xhr = new XMLHttpRequest()
  xhr.open('GET', transformedUrl, true)
  xhr.responseType = 'blob'
  // 如果是转换后的URL，添加自定义请求头
  if (isNeedTransformUrl(fileUrl)) {
    xhr.setRequestHeader('x-custom-header', 'sjgs')
  }
  xhr.onload = () => {
    if (xhr.status === 200) {
      // 创建Blob对象
      const blob = xhr.response
      previewBlobDOCX(blob)
    } else {
      message.error('预览文件失败')
    }
  }
  xhr.onerror = () => {
    message.error('预览文件失败')
  }
  xhr.send()

}
const transformUrl = (url: string): string => {
  if (isNeedTransformUrl(url)) {
    return url.replace('http://10.200.17.131:9000/', 'https://xmgl.gzport.com/minio-download/')
  }
  return url
}
// 判断URL是否需要转换
const isNeedTransformUrl = (url: string): boolean => {
  // 如果当前浏览器地址包含 http://10.200.17.131:90/，则不进行转换
  if (window.location.href.includes('http://10.200.17.131:90')) {
    return false
  }
  // 开发环境，不需要转换
  if (import.meta.env.DEV) {
    return false
  }
  return url.startsWith('http://10.200.17.131:9000')
}
const previewBlobDOCX = (blob) => {
  previewFileBlob.value=blob;
  showPreviewDialog.value = true;
};
// 定义props接收项目组ID
const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  },
  projectGroupCode: {
    type: String,
    required: true
  },
  projectGroupManager: {
    type: String,
    required: false,
    default: ''
  },
  projectManagers: {
    type: Array,
    default: () => []
  },
  isProjectMember: {
    type: Boolean,
    required: false,
    default: false
  }
})
console.log('项目组信息组件接收到的参数2：', {
  projectGroupId: props.id,
  projectGroupCode: props.projectGroupCode
})

const nameOptions = ref<Customer[]>([])

// 获取当前用户角色
const userRole = computed(() => {
  const userInfo = wsCache.get(CACHE_KEY.USER);
  return userInfo?.roles || [];
});

const userDeptId = computed(() => {
  const userInfo = wsCache.get(CACHE_KEY.USER);
  return userInfo?.user?.deptId || null;
});
// 根据部门ID过滤字典项
const getFilteredDictOptions = () => {
  const deptIdStr = String(userDeptId.value);

  // 公司领导（197/115）看到所有四个部门字典的合集
  if (deptIdStr === '197' || deptIdStr === '115') {
    return [
      ...getStrDictOptions('rjyb'),
      ...getStrDictOptions('rjeb'),
      ...getStrDictOptions('xtjc'),
      ...getStrDictOptions('txfw')
    ];
  }

  // 按部门ID匹配对应字典
  if (deptIdStr === '199') {        // 软件一部
    return getStrDictOptions('rjyb');
  } else if (deptIdStr === '201') { // 软件二部
    return getStrDictOptions('rjeb');
  } else if (deptIdStr === '203') { // 系统集成部
    return getStrDictOptions('xtjc');
  } else if (deptIdStr === '202') { // 通信服务部
    return getStrDictOptions('txfw');
  }

  return []; // 默认返回空数组
};




const projectFormRef = ref()
const formRef = ref()
const formProjectRef = ref()
const message = useMessage() // 消息弹窗
const groupFileList = ref([])
const fileList = ref([])
const groupFileList2 = ref([])
const fileList2 = ref([])
const approvalDialogVisible = ref(false) // 弹窗的是否展示
const projectDialogVisible = ref(false) // 弹窗的是否展示
const formProjectLoading = ref(false)
const formManagerRef = ref()
const approvalFormRef = ref()
const formLoading = ref(false)
const router = useRouter()
const route = useRoute()
const itemsLoading = ref(false)
const projectList = ref<ProjectVO[]>([])
const total = ref(0)
const currentProject = ref<ProjectItem>()
const editProjectManagerDialogVisible = ref(false);

const filteredManagingDepartmentList = ref(getStrDictOptions(DICT_TYPE.MANAGING_DEPARTMENT))
const cooperatingDepartmentList = ref(getStrDictOptions(DICT_TYPE.COOPERATING_DEPARTMENT))
const filteredCooperatingDepartmentList = ref(cooperatingDepartmentList.value)

const projectManagerForm = reactive({
  name: ''
});

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectGroupCode: ''
})
// 项目数据
const projectData = ref({
  projectGropName: '',
  projectGropNumber: '',
  startDate: '',
  endDate: '',
  status: '',
  statusText: '',
  projectManager: '',
  type: '',
  percentage: '',
  approved: '',
  nature: '',
  importance: '',
  coefficient: null
})

interface ProjectItem {
  projectGroupId: number // 项目组id
  projectGroupCode: string // 项目组编号
  projectGroupName: string // 项目组名称
  projectCode: string // 项目编号
  busiDepProjectCode: string //业务部项目编号
  projectName: string // 项目名称
  projectContent: string // 项目内容
  managingDepartment: string // 主办部门
  cooperatingDepartment: string // 配合部门
  projectManagerName: string // 项目经理
  totalAmount: number // 金额
  contractId: number // 合同id
  contractCode: string // 合同编号
  contractName: string // 合同名称
  payReciRelation: string // 收付关系
  payerName: string // 付款方
  payerId: number // 付款方
  payerContactor: string // 付款方联系人
  payerContactDetails: string // 付款方联系方式
  payeeName: string // 收款方
  payeeId: number // 收款方
  payeeContactor: string // 收款方联系人
  payeeContactDetails: string // 收款方联系方式
  sourceMethods: string // 来源方式（直接委托、比质比价、公开投标、其他）
  buyMethods: string // 来源方式（直接委托、比质比价、公开投标、其他）
  attribute: string // 属性
  type: string // 类别
  plannedAcceptanceTime: Date // 计划验收时间
  ifAccepted: string // 是否验收（是、否）
  actualAcceptanceTime: Date // 实际验收时间
  ifRelaid: string // 是否超期（是、否）
  paidAmount: number // 已付款金额
  ifPaidFully: string // 是否付款完成（是、否）
  receivedAmount: number // 已收款金额
  ifReceivedFully: string // 是否收款完成（是、否）
  successedBidderCandidate: string // 中标候选人
  remarks: string // 备注
  percentage: string
  id: number
  status: string,
  projectSourceUnitName: string
  projectSourceUnitPerson: string
  projectSourceUnitContactDetails: string
  state: string
  createTime: Date
}

const formData = ref({
  id: undefined,
  projectId: undefined,
  fdTemplataId: undefined,
  projectName: undefined,
  projectIndroduction: undefined,
  mainProjectName: undefined,
  childProjectName: undefined,
  docSubject: undefined,
  projectManager: undefined,
  phone: undefined,
  projectCatagory: undefined,
  estimateTime: undefined,
  source: undefined,
  approvalForm: undefined,
  docCreator: undefined,
  status: undefined,
  importance: undefined,
  nature: undefined,
  projectType: undefined,   // 项目类型
  coefficient: 0,           // 项目系数
})

const formProjectData = ref({
  id: undefined,
  projectId: undefined,
  fdTemplataId: undefined,
  flowId: undefined,
  docCreator: undefined,
  projectName: undefined,
  projectIndroduction: undefined,
  mainProjectName: undefined,
  childProjectName: undefined,
  projectManager: undefined,
  projectCatagory: undefined,
  estimateTime: undefined,
  source: undefined,
  status: undefined,
  estimateMoney: undefined,
  phone: undefined,
  isSiteConstruction: undefined,
})

interface Customer {
  id: number
  name: string
  contacts?: string
  tel?: string
  [key: string]: any
}


const formRules = reactive({
  projectName: [{required: true, message: '项目名称不能为空', trigger: 'blur'}],
  projectManager: [{required: true, message: '项目负责人不能为空', trigger: 'blur'}],
  projectIndroduction: [{required: true, message: '项目简介不能为空', trigger: 'blur'}],
})

const formProjectRules = reactive({
  // projectManager: [{ required: true, message: '请选择项目负责人', trigger: 'blur' },],
  projectIndroduction: [{required: true, message: '项目简介不能为空', trigger: 'blur'}],
  mainProjectName: [{required: true, message: '主项目名称不能为空', trigger: 'blur'}],
  childProjectName: [{required: true, message: '子项目名称不能为空', trigger: 'blur'}],
  // projectCatagory: [{required: true, message: '请选择项目类别', trigger: 'blur'}],
   estimateTime: [{required: true, message: '请选择预计工期', trigger: 'blur'}],
  // source: [{required: true, message: '请输入资金来源', trigger: 'blur'}],
  // phone: [{required: true, message: '请输入联系方式', trigger: 'blur'}],
  estimateMoney: [{required: true, message: '请输入预算金额', trigger: 'blur'}],
  isSiteConstruction: [{required: true, message: '请选择是否涉及现场施工', trigger: 'blur'}],
})

const projectItems = ref<ProjectItem[]>([])
const loading = ref(false)

const dialogVisible = ref(false)
const form = reactive({
  percentage: 0
})

const editProjectDialogVisible = ref(false)
const projectForm = reactive({
  id: null as any,
  percentage: 0,
  projectManagerName: '',
  projectGroupCode: '',
  projectGroupName: '',
  projectCode: '',
  projectName: '',
  projectSourceUnitName: '',
  payReciRelation: '',
  projectSourceUnitPerson: '',
  projectSourceUnitContactDetails: '',
  managingDepartment: '',
  cooperatingDepartment: '',
  projectContent: '',
  type: '',
  state: '',
  remarks: '',
  attribute: '',
  projectGroupId: '',
  importance: undefined,
  nature: undefined,
  projectType: undefined,   // 项目类型
  coefficient: 0,           // 项目系数
  totalAmount: 0,           // 合同金额
})


// 判断当前用户是否有编辑权限
const hasEditPermission = computed(() => {
  // 如果传入了 isProjectMember 属性并且为 true，则允许编辑
/*  if (props.isProjectMember === true) {
    return true
  }*/
  // 1. 检查是否是项目组经理
  const userInfo = wsCache.get(CACHE_KEY.USER);
  const currentUsername = userInfo?.user?.nickname || '';
  const isProjectGroupManager = currentUsername && props.projectGroupManager === currentUsername;
  // 2. 检查是否是超级管理员
  const isAdmin = currentUsername === "超级管理员";
  // 3. 检查是否是项目负责人
  const isProjectManagerInList = props.projectManagers && props.projectManagers.some(
    (manager: any) => manager.projectManagerName === currentUsername
  );
  // 4. 检查是否是一部经理或二部经理
  // const userRoles = userInfo?.roles || [];

  // const isDepartmentManager = userRoles.includes('yb_manager') || userRoles.includes('eb_manager')||userRoles.includes('xtj_manager')

  // return isAdmin || isProjectGroupManager || isProjectManagerInList || isDepartmentManager;
  return isAdmin || isProjectGroupManager || isProjectManagerInList;
});

const handleEditProjectManager = () => {
  // 初始化表单数据
  projectManagerForm.name = projectData.value.projectManager || '';
  editProjectManagerDialogVisible.value = true;
};

const saveProjectManager = async () => {
  try {
    const updateParams: ProjectGropVO = {
      ...projectData.value,
      projectManager: projectManagerForm.name,
      id: props.id.toString()
    }
    await ProjectGropApi.updateProjectGropManager(updateParams);
    // 更新本地数据
    await fetchProjectInfo()
    message.success('项目经理信息已更新');
  } catch (error) {
    message.error('更新失败，请稍后重试');
  } finally {
    // 关闭弹窗
    editProjectManagerDialogVisible.value = false;
  }
};

const handleEditProgress = async (item: ProjectItem) => {
  try {
    // 调用接口获取项目详情数据
    const projectDetail = await ProjectApi.getProject(item.id);
console.log('projectDetail12222222222222222222222222222222222222222211:', projectDetail);
    // 填充表单数据
    projectForm.id = projectDetail.id;
    projectForm.percentage = Number(projectDetail.percentage) || 0;
    projectForm.projectGroupCode = projectDetail.projectGroupCode;
    projectForm.projectGroupName = projectDetail.projectGroupName;
    projectForm.projectCode = projectDetail.projectCode;
    projectForm.projectName = projectDetail.projectName;
    projectForm.projectSourceUnitName = projectDetail.projectSourceUnitName;
    projectForm.payReciRelation = projectDetail.payReciRelation;
    projectForm.projectSourceUnitPerson = projectDetail.projectSourceUnitPerson;
    projectForm.projectSourceUnitContactDetails = projectDetail.projectSourceUnitContactDetails;
    projectForm.projectManagerName = projectDetail.projectManagerName;
    projectForm.managingDepartment = projectDetail.managingDepartment;
    projectForm.cooperatingDepartment = projectDetail.cooperatingDepartment;
    projectForm.projectContent = projectDetail.projectContent;
    projectForm.type = projectDetail.type;
    projectForm.state = projectDetail.state;
    projectForm.remarks = projectDetail.remarks;
    projectForm.attribute = projectDetail.attribute || '非零星';
    projectForm.projectGroupId = projectDetail.projectGroupId;
    projectForm.importance = projectDetail.importance;
    projectForm.nature = projectDetail.nature;
    projectForm.projectType = projectDetail.projectType;
    projectForm.coefficient = projectDetail.coefficient;
    projectForm.totalAmount = projectDetail.totalAmount || 0;
    nameOptions.value = await CustomerApi.getCustomerName();
    editProjectDialogVisible.value = true;

    // 打开编辑对话框后，如果是收款项目且用户为部门经理，则自动计算系数
    if (projectForm.payReciRelation === '收' && isDepartmentManager.value) {
      await calculateCoefficient(false);
    }
  } catch (error) {
    console.error('获取项目详情失败', error);
    ElMessage.error('获取项目详情失败，请稍后重试');
  }
};

const submitProjectPercentage = async () => {
  await projectFormRef.value.validate();
  try {
    itemsLoading.value = true
    const params: any = {
      id: projectForm.id,
      percentage: projectForm.percentage,
      projectGroupCode: projectForm.projectGroupCode,
      projectGroupName: projectForm.projectGroupName,
      projectCode: projectForm.projectCode,
      projectName: projectForm.projectName,
      projectSourceUnitName: projectForm.projectSourceUnitName,
      payReciRelation: projectForm.payReciRelation,
      projectSourceUnitPerson: projectForm.projectSourceUnitPerson,
      projectSourceUnitContactDetails: projectForm.projectSourceUnitContactDetails,
      projectSourceUnitPerson: projectForm.projectSourceUnitPerson,
      managingDepartment: projectForm.managingDepartment,
      cooperatingDepartment: projectForm.cooperatingDepartment,
      projectContent: projectForm.projectContent,
      type: projectForm.type,
      state: projectForm.state,
      remarks: projectForm.remarks,
      attribute: projectForm.attribute,
      projectGroupId: projectForm.projectGroupId,
      importance: projectForm.importance,
      nature: projectForm.nature,
      projectType: projectForm.projectType,
      coefficient: projectForm.coefficient
    };

    if (projectForm.projectManagerName) {
      params.projectManagerName = projectForm.projectManagerName;
    } else {
      params.projectManagerName = '';
    }

    await ProjectApi.updatePercentage(params)
    ElMessage.success('项目更新成功')
    await fetchProjects()
    editProjectDialogVisible.value = false
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    itemsLoading.value = false
  }
}

const openApprovalForm = async () => {
  if(projectData.value.approved == '1') {
    // 添加提示框，如果确认的话则继续执行弹出弹窗，取消的话则return
    const confirmResult = await ElMessageBox.confirm(
      '该项目经理已推送OA审批，是否重复操作？',
      '提示',
      {
        confirmButtonText: '继续推送',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    if (!confirmResult) {
      return
    }
  }
  formManagerRef.value?.resetFields()
  formData.value = await ProjectManagerApprovalApi.getProjectData(props.id.toString())
  await getGroupFileList()
  approvalDialogVisible.value = true
}

const openProjectApprovalForm = async (row) => {
  if(row.approved == '1') {
    // 添加提示框，如果确认的话则继续执行弹出弹窗，取消的话则return
    const confirmResult = await ElMessageBox.confirm(
      '该项目已推送OA审批，是否重复操作？',
      '提示',
      {
        confirmButtonText: '继续推送',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    if (!confirmResult) {
      return
    }
  }
  formProjectRef.value?.resetFields()
  currentProject.value = row
  formProjectData.value = await ProjectApprovalApi.getProjectData(row.id)
  formProjectData.value.projectIndroduction = row.projectContent
  await getGroupFileList2()
  projectDialogVisible.value = true
}

const submitForm = async () => {
  // 校验表单
  await formManagerRef.value.validate()
  formLoading.value = true
  try {
    const flowId = await ProjectManagerApprovalApi.pushProjectManagerApproval({
      projectManagerApproval: formData.value,
      //approvalPageReqVOlist: approvals.value
    })
    console.log('项目经理审批提交成功', flowId)
    approvalDialogVisible.value = false
    if (flowId) {
      await oarouter(flowId)
    }else{
      message.error("未获取到审批流程ID")
    }
    // 发送操作成功的事件
    message.success('推送项目经理成功')
    await fetchProjectInfo()
  }finally {
    formLoading.value = false
  }
}

// 创建防抖后的submitForm函数
const debouncedSubmitForm = debounce(submitForm, 300)

const submitProjectForm = async () => {
  // 校验表单
  await formProjectRef.value.validate()
  // 提交请求
  formProjectLoading.value = true
  try {
    const flowId = await ProjectApprovalApi.pushProjectApproval({
      projectApprovalPageReqVO: formProjectData.value
    })
console.log('项目审批提交成功', flowId)
    projectDialogVisible.value = false
    if (flowId) {
      await oarouter(flowId)
    }else{
      message.error("未获取到审批流程ID")
    }
    // 发送操作成功的事件
    message.success('推送项目成功')
    await fetchProjects()
  } finally {
    formProjectLoading.value = false
  }
}

// 创建防抖后的submitProjectForm函数
const debouncedSubmitProjectForm = debounce(submitProjectForm, 300)

const oarouter = async (flowId: string) => {
  const nickname = await ProjectManagerApprovalApi.getName()
  console.log(nickname)
  const timeTick = Date.now().toString();//时间戳
  const name = nickname ;//获取当前登录用户的姓名
  console.log(name)
  const yonghu = pinyin(name, {style: pinyin.STYLE_NORMAL}).flat().join('');//获取当前登录用户的拼音
  console.log(yonghu)
  const data = await  ProjectManagerApprovalApi.oarouter({
    yonghu:yonghu,
    timeTick:timeTick
  })
  const signature = data          //获取的临时口令
  let encodedFlowId = encodeURIComponent(flowId);
  let nexturl = `/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${encodedFlowId}`
  const url=`https://oa.gzport.com/oa/api/auth/login_by_sso?sso=customer_sso&loginName=${yonghu}&tickTime=${timeTick}&signature=${signature}&next=`+escape(nexturl)
  console.log(url)
  window.open(url);
}


// 根据状态获取状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    '1': 'success', // 进行中
    '4': 'success'     // 已验收
  }
  return statusMap[status] || 'success'
}

// 获取项目信息
const fetchProjectInfo = async () => {
  if (!props.id) {
    return
  }
  try {
    // 这里应该是实际的API调用，获取项目数据
    // const res = await getProjectInfo(props.id)
    // projectData.value = res.data
    loading.value = true
    projectData.value = await ProjectGropApi.getProjectGropStatus(Number(props.id))
    // 模拟数据
    console.log("获取项目组信息，数据8848:", projectData.value)
    console.log('获取项目组信息，ID111111231111111:', props.id)
    loading.value = false
  } catch (error) {
    console.error('获取项目信息失败', error)
  }
}

// 定义事件
const emit = defineEmits(['row-click'])


// 双击事件处理
const handlePercentageDblClick = () => {
  form.percentage = Number(projectData.value.percentage) || 0
  dialogVisible.value = true
}

// 修改项目组进度
const submitPercentage = async () => {
  try {
    loading.value = true
    const updateParams: ProjectGropVO = {
      ...projectData.value,
      percentage: form.percentage.toString(),
      id: props.id.toString()
    }

    await ProjectGropApi.updateProjectGropPercentage(updateParams)
    ElMessage.success('进度更新成功')
    await fetchProjectInfo() // 重新获取最新数据
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    loading.value = false
  }
}

// 处理行点击事件
const handleRowClick = (row) => {
  console.log(row)
  console.log("行单击事件激发!")
  if (!row.contractId){
    return
  }
  // 触发父组件的点击事件
  emit('row-click', row)
  // 触发事件总线，通知导航组件
  emitter.emit('project-item-selected', {
    id: row.id,
    projectName: row.projectName,
    projectCode: row.projectCode,
    projectId: row.id,
    contractId: row.contractId
  })
}

// 格式化金额
const formatMoney = (value) => {
  if (!value) return '-'
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

// 获取项目列表
const fetchProjects = async () => {
  if (!props.projectGroupCode) {
    itemsLoading.value = false
    return
  }

  itemsLoading.value = true
  try {
    queryParams.projectGroupCode = props.projectGroupCode
    console.log('查询项目组下的项目，项目组编号:', props.projectGroupCode)

    // 使用 API 获取项目组下的项目
    const result = await ProjectApi.getProjectByGroupCode(props.projectGroupCode)
    projectList.value = [...result].sort((a, b) => {
      // 优先按合作类型排序：收(0) 在前，付(1) 在后
      const typeOrder = { '收': 0, '付': 1 };
      const aOrder = typeOrder[a.payReciRelation] ?? 2;
      const bOrder = typeOrder[b.payReciRelation] ?? 2;

      // 合作类型不同时按类型排序
      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }

      // 类型相同时按项目编号降序
      return b.projectCode.localeCompare(a.projectCode);
    }) || [];

    total.value = projectList.value.length

    console.log('获取到项目数据111:', projectList.value)

    // 发送项目列表更新事件，以便导航组件可以获取项目列表
    emitter.emit('project-list-updated', projectList.value)
  } catch (error) {
    console.error('获取项目列表失败:', error)
  } finally {
    itemsLoading.value = false
  }
}

// 查看项目详情
const handleViewDetail = (row) => {
  router.push({
    path: '/project/' + row.id
  })
}

// 查看项目合同
const handleViewContract = (row) => {
  router.push({
    path: `/contract/${row.id}/${row.contractId || 'new'}`
  })
}

const handleInput = (value: string) => {
  const numValue = Number(value.replace(/[^0-9]/g, ''));
  if (numValue > 100) {
    form.percentage = '100'; // 限制最大值为 100
  } else if (numValue < 0) {
    form.percentage = '0'; // 限制最小值为 0
  } else {
    form.percentage = String(numValue); // 正常赋值
  }
};

const handleProjectInput = (value: string) => {
  const numValue = Number(value.replace(/[^0-9]/g, ''));
  if (numValue > 100) {
    projectForm.percentage = '100'; // 限制最大值为 100
  } else if (numValue < 0) {
    projectForm.percentage = '0'; // 限制最小值为 0
  } else {
    projectForm.percentage = String(numValue); // 正常赋值
  }
};

const collectedUrls = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  return group.map(item => item.url);
};

const collectUrl2NameMap = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  const url2NameMap = {};
  group.forEach(item => {
    url2NameMap[item.url] = item.name;
  });
  console.log(url2NameMap)
  return url2NameMap;
};

const getGroupFileList = async () => {
  let queryParams = {
    fileBusinessType: `项目经理审批`,
    businessId: props.id.toString(),
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    console.log(queryParams)
    fileList.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < fileList.value.length; i++) {
      let item = fileList.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    groupFileList.value = Object.values(group)
    console.log(groupFileList.value)
  } finally {

  }

};

const getGroupFileList2 = async () => {
  let queryParams = {
    fileBusinessType: `项目审批`,
    businessId: currentProject.value.id,
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    fileList2.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < fileList2.value.length; i++) {
      let item = fileList2.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    groupFileList2.value = Object.values(group)
    console.log(groupFileList2.value)
    console.log("rrrrrrrrrrrrrrrrrrrrr")
  } finally {

  }
};

const tableRef = ref()
// 设置默认选中的项目条目
const setDefaultSelectedRow = async() => {
  // 选中对应的项目条目
  console.log("是否运行")
  console.log(projectList)
  console.log(projectList.value)
  if (projectList.value.length > 0) {
    tableRef.value.setCurrentRow(projectList.value[0]) //  设置默认选中第一行
    console.log(tableRef.value)
    handleRowClick(projectList.value[0]) // 触发行单击事件
  }
}

// 设置排序函数
const payReciSort = (a,b) => {
  const order = {'付': 0 , '收': 1};
  return order[a.payReciRelation] - order[b.payReciRelation];
}

watch(() => [props.id,props.projectGroupCode],  (newVal) => {
  if (newVal) {
    fetchProjectInfo()
    fetchProjects()
  }
}, { immediate: true })

// 检查当前用户是否为部门经理
const checkDepartmentManagerPermission = async () => {
  try {
    const result = await ProjectGropApi.checkDepartmentManager()
    isDepartmentManager.value = result
  } catch (error) {
    console.error('检查部门经理权限失败:', error)
    isDepartmentManager.value = false
  }
}

onMounted(async() => {
  console.log('项目组信息组件挂载完成，参数：', {
    projectGropId: props.id,
    projectGroupCode: props.projectGroupCode
  })

  await checkDepartmentManagerPermission()
  await fetchProjectInfo()
  await fetchProjects()
  await setDefaultSelectedRow()
  await getGroupFileList()
})

watch(
  () => formProjectData.value.projectManager,
  async (newProjectManager) => {
    if (newProjectManager) {
      try {
        const data = await ProjectApprovalApi.getPhone({
          projectManager: newProjectManager
        });
        console.log(data);
        formProjectData.value.phone = data;
        console.log(formProjectData.value.phone);
      } catch (error) {
        console.error('获取联系方式失败', error);
        message.error('获取联系方式失败，请重试');
      }
    } else {
      formProjectData.value.phone = ''; // 如果没有选择项目负责人，则清空联系方式
    }
  }
);

// 监听项目系数计算相关字段的变化，自动计算系数
watch(
  () => [
    projectForm.nature,
    projectForm.importance,
    projectForm.projectType,
    projectForm.managingDepartment,
    projectForm.totalAmount,
    projectForm.payReciRelation
  ],
  async () => {
    // 只有在编辑模式下、收款项目、用户为部门经理且所有必要参数都存在时才自动计算
    if (editProjectDialogVisible.value &&
        projectForm.payReciRelation === '收' &&
        isDepartmentManager.value &&
        projectForm.nature &&
        projectForm.importance &&
        projectForm.projectType &&
        projectForm.managingDepartment &&
        projectForm.totalAmount > 0) {
      await calculateCoefficient(false)
    } else {
      // 参数不完整时重置计算结果
      calculatedCoefficient.value = null
      coefficientError.value = (projectForm.payReciRelation === '收' && isDepartmentManager.value &&
                               (projectForm.nature || projectForm.importance || projectForm.projectType)) ? true : false
    }
  },
  { deep: true }
);

// 选中的项目列表
const selectedProjects = ref([])

// 是否有收款项目被选中
const hasReceivableProject = computed(() => {
  return selectedProjects.value.some(project => project.payReciRelation === '收')
})

// 是否有付款项目被选中
const hasPayableProject = computed(() => {
  return selectedProjects.value.some(project => project.payReciRelation === '付')
})

// 处理项目选择变化
const handleSelectionChange = (selection) => {
  selectedProjects.value = selection
  console.log('选中的项目：', selectedProjects.value)

  // 如果选中了付款项目，给出提示
  if (hasPayableProject.value) {
    message.warning('不能选择付款项目进行关联，请取消勾选付款项目')
  }
}

// 打开表单
const openForm = async (type, projectType) => {
  if (projectType === 'payable' && selectedProjects.value.length === 0) {
    message.error('请先勾选收款项目')
    return
  }

  if (projectType === 'payable' && !hasReceivableProject.value) {
    message.error('请选择至少一个收款类型的项目')
    return
  }

  if (projectType === 'payable' && hasPayableProject.value) {
    message.error('不能选择付款项目进行关联，请取消勾选付款项目')
    return
  }

  // 获取选中的收款项目
  const selectedReceivableProjects = selectedProjects.value.filter(
    project => project.payReciRelation === '收'
  )

  if (selectedReceivableProjects.length === 0 && projectType === 'payable') {
    message.error('请选择至少一个收款类型的项目')
    return
  }

  // 辅助变量
  let typeMap = null
  let managingDepartment = null
  let projectManagerName = null

  // 根据项目具体收/付情况传入数据
  if (projectType === 'payable' && selectedReceivableProjects.length > 0) {
    typeMap = selectedReceivableProjects[0].type
    managingDepartment = selectedReceivableProjects[0].managingDepartment
    projectManagerName = selectedReceivableProjects[0].projectManagerName || ''
  }

  // 打开表单，并传入数据
  formRef.value.open(type, undefined, {
    projectGroupCode: props.projectGroupCode,
    projectGroupName: projectData.value.projectGropName, // 使用项目组数据
    type: typeMap,
    managingDepartment: managingDepartment,
    projectManagerName: projectManagerName,
    payReciRelation: projectType === 'receivable' ? '收' : '付',
    selectedReceivableProjects: selectedReceivableProjects
  })
}

const projectSourceUnitNameChange = async (value) => {
  console.log(value)
  if (value) {
    const data = await CustomerApi.getCustomerPage({
      name: value
    })
    console.log(data.list[0].contacts)
    console.log(data.list[0].tel)
    projectForm.projectSourceUnitPerson = data.list[0].contacts
    projectForm.projectSourceUnitContactDetails = data.list[0].tel
  }
}



// 处理表单提交成功
const handleSuccess = () => {
  // 重新获取项目列表
  fetchProjects()
}

const formRules1 = reactive({
  projectGroupName: [
    {required: true, message: '请输入项目组名称'}
  ],
  projectGroupCode: [
    {required: true, message: '请输入项目组编号'}
  ],
  projectName: [
    {required: true, message: '请输入项目名称'}
  ],
  // totalAmount: [
  //   {required: true, message: '请输入总金额'}
  // ],
  managingDepartment: [
    {required: true, message: '请选择主办部门'}
  ],
  // sourceMethods: [
  //   {required: true, message: '请输入来源方式'}
  // ],
  // payReciRelation: [
  //   {required: true, message: '请输入收款与付款关系'}
  // ],
})

</script>

<style scoped>
.section-block {
  margin-bottom: 40px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  scroll-margin-top: 80px; /* 确保锚点定位有足够的边距 */
}
.project-items-section {
  margin-bottom: 50px;
  scroll-margin-top: 80px;
}
:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}
.project-name-cell {
  white-space: normal;
  word-break: break-all;
}
</style>
