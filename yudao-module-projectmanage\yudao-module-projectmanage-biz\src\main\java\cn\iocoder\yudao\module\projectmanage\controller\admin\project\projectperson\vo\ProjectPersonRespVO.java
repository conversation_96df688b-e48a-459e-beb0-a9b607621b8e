package cn.iocoder.yudao.module.projectmanage.controller.admin.project.projectperson.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 项目人员 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectPersonRespVO {

    @Schema(description = "记录id", example = "26665")
    @ExcelProperty("记录id")
    private Long id;

    @Schema(description = "项目组id", example = "15541")
    @ExcelProperty("项目组id")
    private Long projectGroupId;

    @Schema(description = "项目组编号")
    @ExcelProperty("项目组编号")
    private String projectGroupCode;

    @Schema(description = "项目组名称", example = "李四")
    @ExcelProperty("项目组名称")
    private String projectGroupName;

    @Schema(description = "项目id", example = "3773")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "项目编号")
    @ExcelProperty("项目编号")
    private String projectCode;

    @Schema(description = "项目名称", example = "芋艿")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "姓名", example = "张三")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "部门")
    @ExcelProperty("部门")
    private String department;

    @Schema(description = "岗位")
    @ExcelProperty("岗位")
    private String occupation;

    @Schema(description = "状态", example = "1")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "删除符（0未删除1已删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("删除符（0未删除1已删除）")
    private Boolean deleted;

    @Schema(description = "系统名")
    private  String systemName;

    @Schema(description = "系统ID")
    private String systemId;

    @Schema(description = "比例")
    private String percentage;

    @Schema(description = "时间")
    private String time;

    @Schema(description = "经理是否确认")
    private String managerConfirm;

    @Schema(description = "驳回原因")
    @ExcelProperty("驳回原因")
    private String rejectReason;

    @Schema(description = "经理驳回时间")
    @ExcelProperty("经理驳回时间")
    private LocalDateTime managerRejectTime;

    @Schema(description = "经理操作时间")
    @ExcelProperty("经理操作时间")
    private LocalDateTime managerOperateTime;
}