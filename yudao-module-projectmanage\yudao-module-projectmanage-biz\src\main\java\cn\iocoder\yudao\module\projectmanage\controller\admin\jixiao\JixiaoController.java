package cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.jixiao.JixiaoDO;
import cn.iocoder.yudao.module.projectmanage.service.jixiao.JixiaoService;

@Tag(name = "管理后台 - 绩效考核")
@RestController
@RequestMapping("/projectmanage/jixiao")
@Validated
public class JixiaoController {

    @Resource
    private JixiaoService jixiaoService;

    @PostMapping("/create")
    @Operation(summary = "创建绩效考核")
    public CommonResult<Long> createJixiao(@Valid @RequestBody JixiaoSaveReqVO createReqVO) {
        return success(jixiaoService.createJixiao(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新绩效考核")
    public CommonResult<Boolean> updateJixiao(@Valid @RequestBody JixiaoSaveReqVO updateReqVO) {
        jixiaoService.updateJixiao(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除绩效考核")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteJixiao(@RequestParam("id") Long id) {
        jixiaoService.deleteJixiao(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得绩效考核")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<JixiaoRespVO> getJixiao(@RequestParam("id") Long id) {
        JixiaoDO jixiao = jixiaoService.getJixiao(id);
        return success(BeanUtils.toBean(jixiao, JixiaoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得绩效考核分页")
    public CommonResult<PageResult<JixiaoRespVO>> getJixiaoPage(@Valid JixiaoPageReqVO pageReqVO) {
        PageResult<JixiaoDO> pageResult = jixiaoService.getJixiaoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, JixiaoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出绩效考核 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportJixiaoExcel(@Valid JixiaoPageReqVO pageReqVO,
                                  HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<JixiaoDO> list = jixiaoService.getJixiaoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "绩效考核.xls", "数据", JixiaoRespVO.class,
                BeanUtils.toBean(list, JixiaoRespVO.class));
    }

    @GetMapping("/department-performance")
    @Operation(summary = "获取部门经理绩效表数据")
    public CommonResult<List<DepartmentPerformanceRespVO>> getDepartmentPerformanceData(@Valid DepartmentPerformanceReqVO reqVO) {
        List<DepartmentPerformanceRespVO> data = jixiaoService.getDepartmentPerformanceData(reqVO);
        return success(data);
    }

    @PostMapping("/confirm-department-performance")
    @Operation(summary = "确认部门绩效占比")
    public CommonResult<Boolean> confirmDepartmentPerformance(@Valid @RequestBody ConfirmDepartmentPerformanceReqVO reqVO) {
        jixiaoService.confirmDepartmentPerformance(reqVO);
        return success(true);
    }

    @PostMapping("/reject-department-performance")
    @Operation(summary = "驳回部门绩效占比")
    public CommonResult<Boolean> rejectDepartmentPerformance(@Valid @RequestBody RejectDepartmentPerformanceReqVO reqVO) {
        jixiaoService.rejectDepartmentPerformance(reqVO);
        return success(true);
    }

    @PostMapping("/revoke-reject-department-performance")
    @Operation(summary = "撤销驳回部门绩效占比")
    public CommonResult<Boolean> revokeRejectDepartmentPerformance(@Valid @RequestBody RevokeRejectDepartmentPerformanceReqVO reqVO) {
        jixiaoService.revokeRejectDepartmentPerformance(reqVO);
        return success(true);
    }

}