import request from '@/config/axios'
import {UnwrapRef} from "vue";

// 项目组 VO
export interface ProjectGropVO {
  projectGropNumber: string // 项目组编号
  projectGropName: string // 项目组名称
  receivable: number // 收款金额
  receivedAmount: number // 已收款金额
  payable: number // 付款金额
  paidAmount: number // 已付款金额
  profit: number // 利润
  remarks: string // 备注
  type: string[] // 类型
  percentage: string
  projectManager: string // 项目经理
  ownerName: string[]
  generalContractor: string[]
  subcontractorSecond: string[]
  subcontractorThird: string[]
  subcontractorFourth: string[]
  contractAmounts?: {
    receivableAmount: number
    payableAmount: number
  }
  paymentAmounts?: {
    invAmount: number
    invPayAmount: number
  }
  deptId?: number // 当前登录用户部门ID
  checkAccepted: string // 是否验收
  handler: string // 项目经办人
  amountScale: string // 金额规模
  nature: string // 性质
  importance: string // 重要性
  coefficient: number // 项目系数
  creator: string // 创建者ID
  creatorNickname: string // 创建者昵称
  performanceInfo: string // 绩效信息
}

// 项目组 API
export const ProjectGropApi = {


  // 查询项目组分页
  getProjecGroupNowAmounttByGroupCodeAndYwType: async (params: any) => {
    return await request.get({ url: `/projectmanage/project-grop/getProjecGroupNowAmounttByGroupCodeAndYwType`, params })
  },

  /**
   * 检查用户对项目组的权限
   * @param params 参数对象，包含projectGroupId、projectId(可选)和systemId(可选)
   * @returns 返回权限检查结果，包含权限标志位和相关ID
   */
  checkPermission: async (params: { projectGroupId: string; projectId?: string; systemId?: string }) => {
    return await request.get({ url: `/projectmanage/project-grop/check-permission`, params })
  },

  // 查询项目组分页
  getProjectGropPage: async (params: any) => {
    return await request.get({ url: `/projectmanage/project-grop/page`, params })
  },

  getProjectGropPageWithputManager: async (params: any) => {
    return await request.get({ url: `/projectmanage/project-grop/pageWithoutManager`, params })
  },

  // 查询项目组详情
  getProjectGrop: async (id: number) => {
    return await request.get({ url: `/projectmanage/project-grop/get?id=` + id })
  },

  getStatus: async (projectGropId: number) => {
    return await request.get({ url: `/projectmanage/project-grop/status?projectGropId=` + projectGropId })
  },

  // 新增项目组
  createProjectGrop: async (data: ProjectGropVO) => {
    return await request.post({ url: `/projectmanage/project-grop/create`, data })
  },

  // 修改项目组
  updateProjectGrop: async (data: ProjectGropVO) => {
    return await request.put({ url: `/projectmanage/project-grop/update`, data })
  },

  // 修改项目组进度
  updateProjectGropPercentage: async (data: ProjectGropVO) => {
    return await request.put({ url: `/projectmanage/project-grop/percentage`, data })
  },

  updateProjectGropManager: async (data: ProjectGropVO) => {
    return await request.put({ url: `/projectmanage/project-grop/projectManage`, data })
  },

  // 删除项目组
  deleteProjectGrop: async (id: number) => {
    return await request.delete({ url: `/projectmanage/project-grop/delete?id=` + id })
  },

  // 导出项目组 Excel
  exportProjectGrop: async (params) => {
    return await request.download({ url: `/projectmanage/project-grop/export-excel`, params })
  },

  /** 根据项目组编号获取详情 */
  getProjectGropByNumber: async (projectGropNumber: string) => {
    return await request.get({ url: `/projectmanage/project-grop/get-by-number/${projectGropNumber}` })
  },

  /** 获取项目组的收款项目及费项 */
  getReceivableProjects: async (projectGropNumber: string) => {
    return await request.get({ url: `/projectmanage/project-grop/receivable-projects/${projectGropNumber}` })
  },

  /** 获取项目组的付款项目及费项 */
  getPayableProjects: async (projectGropNumber: string) => {
    return await request.get({ url: `/projectmanage/project-grop/payable-projects/${projectGropNumber}` })
  },

  /** 获取项目组的合同金额统计 */
  getContractAmounts: async (projectGropNumber: string) => {
    return await request.get({ url: `/projectmanage/project-grop/contract-amounts/${projectGropNumber}` })
  },

  // 生成项目组编号
  generateProjectGropNumber: async () => {
    return await request.put({ url: '/projectmanage/project-grop/generate-number' })
  },

  // 自动计算项目系数
  calculateProjectCoefficient: async (data: {
    deptId: number
    nature: string
    importance: string
    receivable: number
    type: string[]
  }) => {
    return await request.post({ url: '/projectmanage/project-grop/calculate-coefficient', data })
  },

  // 获取项目组的合同金额统计（与列表页面相同的计算逻辑）
  getContractAmounts: async (projectGropNumber: string) => {
    return await request.get({ url: `/projectmanage/project-grop/contract-amounts/${projectGropNumber}` })
  },
  /** 生成指定年份项目组编号*/
  generateAppointProjectGropNumber: async (year: number) => {
    return await request.get({ url: `/projectmanage/project-grop/generate-number-appoint/${year}` })
  },
  /** 获取项目组的开票和收票总额 */
  getPaymentAmounts: async (projectGropNumber: string) => {
    return await request.get({ url: `/projectmanage/project-grop/get-payment-amounts/${projectGropNumber}` })
  },

  // 合并项目组信息
  mergeProjectGroup: async (data: Array<ProjectGropVO>, projectGroupName: string) => {
    return await request.post({ url: `/projectmanage/project-grop/createMergeGroup/${projectGroupName}`,data })
  },
  // 合并项目组信息
  // mergeProjectGroup: async (projectGroupName: string) => {
  //   return await request.post({ url: `/projectmanage/project-grop/createMergeGroup/${projectGroupName},data`})
  // }

  getProjectData: async (id: number) => {
    return await request.get({ url: `/projectmanage/project-grop/list?id=` + id })
  },

  // 获取项目所有阶段信息
  getStageInfo: async (params: { projectGroupId: string; projectId?: string; systemId?: string }) => {
    return await request.get({ url: `/projectmanage/project-grop/get-stage-info`, params })
  },

  getProjectGropStatus: async (id: number) => {
    return await request.get({ url: `/projectmanage/project-grop/getNew?id=` + id })
  },

  /**
   * 获取项目相关文档
   * @param params 参数对象，包含projectId和projectGroupId
   * @returns 返回合同、运维、上线、实施的文档数据
   */
  getProjectDocuments: async (params: { projectId?: string; projectGroupId?: string; systemId?: string }) => {
    return await request.get({ url: `/projectmanage/project-grop/get-project-documents`, params })
  },

  /**
   * 获取项目系统记录列表
   * @param params 参数对象，包含projectId和projectGroupId
   * @returns 返回系统记录列表数据
   */
  getSystemListByProject: async (params: { projectId?: string; projectGroupId: string }) => {
    return await request.get({ url: `/projectmanage/system/list-by-project`, params })
  },

  /**
   * 按部门查询项目组
   * @param department 部门名称
   * @param pageNo 页码，默认为1
   * @param pageSize 每页数量，默认为50
   * @returns 返回部门相关的项目组列表
   */
  getProjectGropByDepartment: async (department: string, pageNo: number = 1, pageSize: number = 50) => {
    return await request.get({ url: `/projectmanage/project-grop/by-department`, params: { department, pageNo, pageSize } })
  },
  /**
   * 当修改项目组编号时，同时修改项目组相关联的其他表的信息
   */
  updateOtherTables: async (oldProjectGropNumber: String, projectGropNumber: string) => {
    return await request.post({ url: `/projectmanage/project-grop/updateOtherTables/${oldProjectGropNumber}/${projectGropNumber}`})
  },
  /**
   * 根据年份查询项目组记录
   */
   selectListByYear: async (year?: string, pageNo: number = 1, pageSize: number = 50) => {
     return await request.get({ url: `/projectmanage/project-grop/selectByYear`, params: { year, pageNo, pageSize } })
  },
  getEmptyManagerCount: async (params: any) => {
    return await request.get({ url: `/projectmanage/project-grop/count-empty-manager` ,
      params
    })
  },
  /**
   * 用已知年份修改合同编号
   */
  updateContractYear: async (year: UnwrapRef<FormData["year"]>, projectGropNumber: UnwrapRef<FormData["projectGropNumber"]>) => {
    return await request.post({ url: `/projectmanage/project-grop/updateContractYear/${year}/${projectGropNumber}`})
  }

}
