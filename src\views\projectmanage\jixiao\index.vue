<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
<!--      <el-form-item label="合同id" prop="contractId">-->
<!--        <el-input-->
<!--          v-model="queryParams.contractId"-->
<!--          placeholder="请输入合同id"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="项目id" prop="projectId">-->
<!--        <el-input-->
<!--          v-model="queryParams.projectId"-->
<!--          placeholder="请输入项目id"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目性质" prop="projectNature">
        <el-input
          v-model="queryParams.projectNature"
          placeholder="请输入项目性质"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目类别" prop="projectType">
        <el-select
          v-model="queryParams.projectType"
          placeholder="请选择项目类别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(`project_type`)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="项目实施系数" prop="projectCoefficient">-->
<!--        <el-input-->
<!--          v-model="queryParams.projectCoefficient"-->
<!--          placeholder="请输入项目实施系数"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="合同工期" prop="contractWorkday">-->
<!--        <el-input-->
<!--          v-model="queryParams.contractWorkday"-->
<!--          placeholder="请输入合同工期"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="年内项目实施时间" prop="projectWorkday">-->
<!--        <el-input-->
<!--          v-model="queryParams.projectWorkday"-->
<!--          placeholder="请输入年内项目实施时间"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="项目人数" prop="personNum">
        <el-input
          v-model="queryParams.personNum"
          placeholder="请输入项目成员人数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目负责人" prop="projectManage" label-width="100px">
        <el-input
          v-model="queryParams.projectManage"
          placeholder="请输入项目负责人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目人员" prop="projectPerson">
        <el-input
          v-model="queryParams.projectPerson"
          placeholder="请输入项目人员"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="人员参与比例" prop="projectProportions">-->
<!--        <el-input-->
<!--          v-model="queryParams.projectProportions"-->
<!--          placeholder="请输入人员参与比例"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="季度工期" prop="seasonWorkday">-->
<!--        <el-input-->
<!--          v-model="queryParams.seasonWorkday"-->
<!--          placeholder="请输入季度工期"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="季度奖金分配" prop="seasonAllocate">-->
<!--        <el-input-->
<!--          v-model="queryParams.seasonAllocate"-->
<!--          placeholder="请输入季度奖金分配"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="个人季度分配" prop="seasonPersonallocate">-->
<!--        <el-input-->
<!--          v-model="queryParams.seasonPersonallocate"-->
<!--          placeholder="请输入个人季度分配"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="年度工期" prop="annualWorkday">-->
<!--        <el-input-->
<!--          v-model="queryParams.annualWorkday"-->
<!--          placeholder="请输入年度工期"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="年度奖金分配" prop="annualAllocate">-->
<!--        <el-input-->
<!--          v-model="queryParams.annualAllocate"-->
<!--          placeholder="请输入年度奖金分配"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="个人年度分配" prop="annualPersonallocate">-->
<!--        <el-input-->
<!--          v-model="queryParams.annualPersonallocate"-->
<!--          placeholder="请输入个人年度分配"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="超期情况" prop="exWorkday">-->
<!--        <el-input-->
<!--          v-model="queryParams.exWorkday"-->
<!--          placeholder="请输入超期情况"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="扣减" prop="deductions">-->
<!--        <el-input-->
<!--          v-model="queryParams.deductions"-->
<!--          placeholder="请输入扣减"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="备注" prop="remarks">-->
<!--        <el-input-->
<!--          v-model="queryParams.remarks"-->
<!--          placeholder="请输入备注"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="创建时间" prop="createTime">-->
<!--        <el-date-picker-->
<!--          v-model="queryParams.createTime"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          type="daterange"-->
<!--          start-placeholder="开始日期"-->
<!--          end-placeholder="结束日期"-->
<!--          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"-->
<!--          class="!w-220px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <!-- 注释掉原来的部门绩效表确认按钮 -->
        <!--
        <el-button
          v-if="canConfirmPerformance"
          type="warning"
          plain
          @click="handleConfirmPerformance"
          :loading="confirmLoading"
        >
          <Icon icon="ep:check" class="mr-5px" /> 部门绩效表确认
        </el-button>
        -->
        <el-button
          v-if="canConfirmPerformance"
          type="primary"
          @click="handleDepartmentPerformance"
          :loading="departmentLoading"
          class="performance-confirm-btn"
        >
          <Icon icon="ep:document-checked" class="mr-5px" /> 部门绩效表确认
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list"  :show-overflow-tooltip="true" :span-method="mergeCells">
<!--      <el-table-column label="id" align="center" prop="id" />-->
     <!-- <el-table-column label="合同id" align="center" prop="contractId" />
     <el-table-column label="项目id" align="center" prop="projectId" /> -->
      <el-table-column label="月期" align="center" prop="yearmonth" fixed="left"/>
      <el-table-column label="项目名称" align="center" prop="projectName" fixed="left"/>
      <el-table-column label="项目性质" align="center" prop="projectNature" />
      <el-table-column label="项目类别" align="center" prop="projectType" />
      <el-table-column label="项目实施系数" align="center" prop="projectCoefficient" />
      <el-table-column label="合同工期" align="center" prop="contractWorkday" />
      <el-table-column label="年内项目实施时间" align="center" prop="projectWorkday" />
      <el-table-column label="项目成员人数" align="center" prop="personNum" />
      <el-table-column label="项目负责人" align="center" prop="projectManage" />
      <el-table-column label="季度工期" align="center" prop="seasonWorkday" />
      <el-table-column label="季度奖金分配" align="center" prop="seasonAllocate" />
      <el-table-column label="年度工期" align="center" prop="annualWorkday" />
      <el-table-column label="年度奖金分配" align="center" prop="annualAllocate" />
      <el-table-column label="项目人员" align="center" prop="projectPerson" />
      <el-table-column label="个人参与比例" align="center" prop="projectProportions" />
      <el-table-column label="个人季度分配" align="center" prop="seasonPersonallocate" />
      <el-table-column label="个人年度分配" align="center" prop="annualPersonallocate" />
      <el-table-column label="超期情况" align="center" prop="exWorkday" />
      <el-table-column label="扣减" align="center" prop="deductions" />
      <el-table-column label="备注" align="center" prop="remarks" />

<!--      <el-table-column-->
<!--        label="创建时间"-->
<!--        align="center"-->
<!--        prop="createTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="180px"-->
<!--      />-->
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <JixiaoForm ref="formRef" @success="getList" />

  <!-- 部门绩效表弹窗 -->
  <el-dialog
    v-model="performanceDialogVisible"
    :title="`${currentDepartment}绩效表`"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    top="5vh"
    :modal="true"
    :lock-scroll="true"
    class="performance-dialog"
  >
    <div class="performance-iframe-container" @wheel.stop>
      <iframe
        v-if="performanceUrl"
        :src="performanceUrl"
        width="100%"
        height="100%"
        frameborder="0"
        allowfullscreen
        loading="lazy"
        scrolling="yes"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
      ></iframe>
      <div v-else class="loading-placeholder">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载绩效表...</span>
      </div>
    </div>
    <template #footer>
      <el-button @click="performanceDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="openInNewWindow">在新窗口中打开</el-button>
    </template>
  </el-dialog>

  <!-- 部门绩效表确认弹窗 -->
  <el-dialog
    v-model="departmentPerformanceDialogVisible"
    title="部门绩效表确认"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    top="5vh"
    :modal="true"
    :lock-scroll="true"
    class="department-performance-dialog"
  >
    <div class="dialog-content">
      <!-- 筛选表单 -->
      <el-form :model="departmentFilterParams" inline >
        <el-form-item label="月期" prop="monthPeriod">
          <el-date-picker
            v-model="departmentFilterParams.monthPeriod"
            type="month"
            placeholder="选择月期"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 150px"
            clearable
          />
        </el-form-item>
        <el-form-item label="主办部门" prop="managingDepartment">
          <el-select
            v-model="departmentFilterParams.managingDepartment"
            placeholder="选择主办部门"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="dept in allowedDepartments"
              :key="dept"
              :label="dept"
              :value="dept"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleDepartmentQuery">
            <Icon icon="ep:search" class="mr-5px" /> 查询
          </el-button>
          <el-button @click="handleDepartmentReset">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 页签组件 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="performance-tabs">
        <el-tab-pane label="待确认" name="pending">
          <!-- 待确认数据内容 -->
          <div class="tab-content">
            <!-- 勾选统计信息 -->
            <div class="selection-info" v-if="departmentPerformanceData.length > 0">
              <span>已勾选 <strong>{{ selectedProjectsCount }}</strong> 个项目，共 <strong>{{ totalProjectsCount }}</strong> 个项目</span>
              <el-button
                type="text"
                size="small"
                @click="toggleSelectAll"
              >
                {{ isAllProjectsSelected ? '取消全选' : '全选' }}
              </el-button>
            </div>

            <!-- 表格容器 -->
            <div class="table-container">
              <el-table
                ref="tableRef"
                :data="departmentPerformanceData"
                v-loading="departmentLoading"
                :show-overflow-tooltip="true"
                height="65vh"
                style="width: 100%"
                :span-method="departmentMergeCells"
                border
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="50" fixed="left" />
                <el-table-column label="月期" prop="monthPeriod" width="80" fixed="left" />
                <el-table-column label="项目名称" prop="projectName" width="170" fixed="left" />
                <el-table-column label="项目性质" prop="projectNature" width="90" />
                <el-table-column label="项目类型" prop="projectType" width="90"/>
                <el-table-column label="项目规模" prop="totalAmount" width="100" />
                <el-table-column label="项目重要性" prop="importance" width="95"/>
                <el-table-column label="主办部门" prop="managingDepartment" width="120" />
                <el-table-column label="项目类别" prop="projectType" width="90" />
                <el-table-column label="类型" prop="type" width="80" />
                <el-table-column label="系数" prop="projectCoefficient" width="80" />
                <el-table-column label="项目经理" prop="projectManagerName" width="85">
                  <template #default="scope">
                    {{ scope.row.projectManagerName || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="合同工期" prop="contractDuration" width="85">
                  <template #default="scope">
                    {{ scope.row.contractDuration || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="成员数量" prop="memberCount" width="85" />
                <el-table-column label="成员姓名" prop="memberName" width="85" />
                <el-table-column label="占比(%)" prop="personalPercentage" width="80" />
              </el-table>
            </div>
          </div>
        </el-tab-pane>

                 <el-tab-pane label="已驳回" name="rejected">
           <!-- 已驳回数据内容 -->
           <div class="tab-content">
             <!-- 勾选统计信息 -->
             <div class="selection-info" v-if="departmentPerformanceData.length > 0">
               <span>已勾选 <strong>{{ selectedRejectedProjectsCount }}</strong> 个项目，共 <strong>{{ totalProjectsCount }}</strong> 个项目</span>
               <el-button
                 type="text"
                 size="small"
                 @click="toggleSelectAllRejected"
               >
                 {{ isAllRejectedProjectsSelected ? '取消全选' : '全选' }}
               </el-button>
             </div>

             <!-- 表格容器 -->
             <div class="table-container">
               <el-table
                 ref="rejectedTableRef"
                 :data="departmentPerformanceData"
                 v-loading="departmentLoading"
                 :show-overflow-tooltip="true"
                 height="65vh"
                 style="width: 100%"
                 :span-method="departmentMergeCells"
                 border
                 @selection-change="handleRejectedSelectionChange"
               >
                 <el-table-column type="selection" width="50" fixed="left" />
                 <el-table-column label="月期" prop="monthPeriod" width="80" fixed="left" />
                 <el-table-column label="项目名称" prop="projectName" width="170" fixed="left" />
                 <el-table-column label="项目性质" prop="projectNature" width="90" />
                 <el-table-column label="项目类型" prop="projectType" width="90"/>
                 <el-table-column label="项目规模" prop="totalAmount" width="100" />
                 <el-table-column label="项目重要性" prop="importance" width="95"/>
                 <el-table-column label="主办部门" prop="managingDepartment" width="110" />
                 <el-table-column label="项目类别" prop="projectType" width="90" />
                 <el-table-column label="类型" prop="type" width="60" />
                 <el-table-column label="系数" prop="projectCoefficient" width="70" />
                 <el-table-column label="项目经理" prop="projectManagerName" width="85">
                   <template #default="scope">
                     {{ scope.row.projectManagerName || '-' }}
                   </template>
                 </el-table-column>
                 <el-table-column label="工期" prop="contractDuration" width="80">
                   <template #default="scope">
                     {{ scope.row.contractDuration || '-' }}
                   </template>
                 </el-table-column>
                 <el-table-column label="成员姓名" prop="memberName" width="85" />
                 <el-table-column label="占比(%)" prop="personalPercentage" width="80" />
                 <el-table-column label="驳回原因" prop="rejectReason" width="150">
                   <template #default="scope">
                     <el-tooltip :content="scope.row.rejectReason" placement="top">
                       <span>{{ scope.row.rejectReason || '-' }}</span>
                     </el-tooltip>
                   </template>
                 </el-table-column>
               </el-table>
             </div>
           </div>
         </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <el-button @click="departmentPerformanceDialogVisible = false">关闭</el-button>
      <!-- 只在待确认页签显示确认和驳回按钮 -->
      <template v-if="activeTab === 'pending'">
        <el-button
          type="danger"
          @click="handleRejectDepartmentPerformance"
          :loading="rejectDepartmentLoading"
          :disabled="selectedProjectsCount === 0"
        >
          驳回绩效表 ({{ selectedProjectsCount }}个项目)
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirmDepartmentPerformance"
          :loading="confirmDepartmentLoading"
          :disabled="selectedProjectsCount === 0"
        >
          确认部门绩效表 ({{ selectedProjectsCount }}个项目)
        </el-button>
      </template>
      <!-- 只在已驳回页签显示撤销驳回按钮 -->
      <template v-if="activeTab === 'rejected'">
        <el-button
          type="warning"
          @click="handleRevokeRejectDepartmentPerformance"
          :loading="revokeRejectLoading"
          :disabled="selectedRejectedProjectsCount === 0"
        >
          撤销驳回 ({{ selectedRejectedProjectsCount }}个项目)
        </el-button>
      </template>
    </template>
  </el-dialog>

  <!-- 驳回原因输入弹窗 -->
  <el-dialog
    v-model="rejectReasonDialogVisible"
    title="驳回绩效占比"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="reject-dialog"
  >
    <div class="reject-content">
      <div class="reject-info">
        <el-icon class="reject-icon"><WarningFilled /></el-icon>
        <div class="reject-text">
          <p><strong>即将驳回 {{ selectedProjectsCount }} 个项目的绩效占比数据</strong></p>
          <p class="reject-desc">驳回后，该项目的所有员工绩效占比都需要重新填写</p>
        </div>
      </div>

      <el-form ref="rejectFormRef" :model="rejectForm" :rules="rejectFormRules" label-width="80px">
        <el-form-item label="驳回原因" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回原因，说明绩效占比存在的问题..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="rejectReasonDialogVisible = false">取消</el-button>
      <el-button
        type="danger"
        @click="handleConfirmReject"
        :loading="rejectDepartmentLoading"
      >
        确认驳回 ({{ selectedProjectsCount }}个项目)
      </el-button>
    </template>
  </el-dialog>

</template>

<script setup lang="ts">

import download from '@/utils/download'
import { JixiaoApi, JixiaoVO, DepartmentPerformanceVO } from '@/api/projectmanage/jixiao'
import JixiaoForm from './JixiaoForm.vue'
import {getStrDictOptions} from "@/utils/dict";
import { computed, watch, nextTick } from 'vue';
import { useUserStore } from '@/store/modules/user';

/** 绩效考核 列表 */
defineOptions({ name: 'Jixiao' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const userStore = useUserStore() // 用户store

const loading = ref(true) // 列表的加载中
const list = ref<JixiaoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const confirmLoading = ref(false) // 确认按钮的加载中
const departmentLoading = ref(false) // 部门绩效表确认按钮的加载中
const confirmDepartmentLoading = ref(false) // 确认部门绩效表按钮的加载中
const rejectDepartmentLoading = ref(false) // 驳回部门绩效表按钮的加载中
const revokeRejectLoading = ref(false) // 撤销驳回按钮的加载中

// 部门绩效表弹窗相关
const performanceDialogVisible = ref(false) // 弹窗显示状态
const performanceUrl = ref('') // iframe的URL
const currentDepartment = ref('') // 当前部门名称

// 部门绩效表确认弹窗相关
const departmentPerformanceDialogVisible = ref(false) // 部门绩效表确认弹窗显示状态
const departmentPerformanceData = ref<DepartmentPerformanceVO[]>([]) // 部门绩效表确认数据
const selectedDepartmentData = ref<DepartmentPerformanceVO[]>([]) // 勾选的绩效数据
const selectedRejectedData = ref<DepartmentPerformanceVO[]>([]) // 已驳回页签中勾选的绩效数据
const activeTab = ref('pending') // 当前激活的页签：pending-待确认，rejected-已驳回

// 驳回相关
const rejectReasonDialogVisible = ref(false) // 驳回原因弹窗显示状态
const rejectForm = reactive({
  reason: ''
})
const rejectFormRules = reactive({
  reason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
})
const rejectFormRef = ref() // 驳回表单引用

/** 获取当前月份 */
const getCurrentMonth = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
}

// 部门绩效表确认筛选参数
const departmentFilterParams = reactive({
  monthPeriod: getCurrentMonth(),
  managingDepartment: ''
})
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  contractId: undefined,
  projectId: undefined,
  projectName: undefined,
  projectNature: undefined,
  projectType: undefined,
  projectCoefficient: undefined,
  contractWorkday: undefined,
  projectWorkday: undefined,
  personNum: undefined,
  projectManage: undefined,
  projectPerson: undefined,
  projectProportions: undefined,
  seasonWorkday: undefined,
  seasonAllocate: undefined,
  seasonPersonallocate: undefined,
  annualWorkday: undefined,
  annualAllocate: undefined,
  annualPersonallocate: undefined,
  exWorkday: undefined,
  deductions: undefined,
  remarks: undefined,
  yearmonth: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 权限检查：只有特定用户可以看到部门绩效表确认按钮
const canConfirmPerformance = computed(() => {
  const nickname = userStore.user.nickname
  const allowedUsers = ['陈华', '甘永嘉', '潘松涛', '超级管理员']
  return allowedUsers.includes(nickname)
})

// 根据用户权限获取可选择的部门列表
const allowedDepartments = computed(() => {
  const nickname = userStore.user.nickname

  // 超级管理员可以看到所有部门
  if (nickname === '超级管理员') {
    return ['系统集成部', '软件开发一部', '软件开发二部', '通信服务部']
  }

  // 根据部门经理权限配置
  switch (nickname) {
    case '陈华':
      return ['软件开发一部']
    case '甘永嘉':
      return ['软件开发二部']
    case '潘松涛':
      return ['系统集成部']
    default:
      return [] // 其他用户无权限
  }
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await JixiaoApi.getJixiaoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await JixiaoApi.deleteJixiao(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await JixiaoApi.exportJixiao(queryParams)
    download.excel(data, '绩效考核.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 部门绩效表确认按钮操作 */
const handleConfirmPerformance = async () => {
  try {
    // 确认的二次确认 查看还要二次确认，你AI写得？
    // await message.confirm('确定要查看部门绩效表吗？', '提示')
    confirmLoading.value = true

    // 根据用户昵称确定部门
    const nickname = userStore.user.nickname
    let department = ''

    switch (nickname) {
      case '陈华':
        department = '软件开发一部'
        break
      case '甘永嘉':
        department = '软件开发二部'
        break
      case '潘松涛':
        department = '系统集成部'
        break
      case '超级管理员':
        break
      default:
        message.error('无权限访问')
        return
    }

    // 获取当前月份 (YYYY-MM格式)
    const currentDate = new Date()
    const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`

    // 构建URL参数
    const baseUrl = 'https://xmgl.gzport.com/jmreport/view/1107180908913168384'
    const params = new URLSearchParams({
      'performance01__managing_department': department,
      'performance01__month_period_begin': currentMonth,
      'performance01__month_period_end': currentMonth
    })

    const fullUrl = `${baseUrl}?${params.toString()}`

    // 调试信息：打印构建的URL
    console.log('绩效表URL:', fullUrl)
    console.log('部门:', department)
    console.log('当前月份:', currentMonth)

    // 设置弹窗数据并显示
    currentDepartment.value = department
    performanceUrl.value = fullUrl
    performanceDialogVisible.value = true

    message.success(`正在加载${department}绩效表...`)
  } catch {
  } finally {
    confirmLoading.value = false
  }
}

/** 部门绩效表确认按钮操作 */
const handleDepartmentPerformance = async () => {
  try {
    departmentLoading.value = true

    // 权限控制：为部门经理自动设置默认部门
    const nickname = userStore.user.nickname
    if (nickname !== '超级管理员' && allowedDepartments.value.length === 1) {
      // 如果只有一个部门权限，自动设置为默认值
      departmentFilterParams.managingDepartment = allowedDepartments.value[0]
    }

    // 显示弹窗
    departmentPerformanceDialogVisible.value = true

    // 获取数据
    await loadDepartmentPerformanceData()

  } catch (error) {
    console.error('获取部门绩效表确认数据失败:', error)
    message.error('获取数据失败，请稍后重试')
  } finally {
    departmentLoading.value = false
  }
}

/** 加载部门绩效表确认数据 */
const loadDepartmentPerformanceData = async () => {
  try {
    departmentLoading.value = true

    // 权限控制：部门经理必须传递部门参数
    const nickname = userStore.user.nickname
    const isDepartmentManager = ['陈华', '甘永嘉', '潘松涛'].includes(nickname)

    if (isDepartmentManager && !departmentFilterParams.managingDepartment) {
      message.warning('请选择主办部门后再查询')
      return
    }

    // 根据当前页签设置状态参数
    const params = {
      ...departmentFilterParams,
      status: activeTab.value // pending-待确认，rejected-已驳回
    }

    // 调用后端接口获取数据
    const data = await JixiaoApi.getDepartmentPerformanceData(params)
    // 按项目名称分组排序，确保相同项目的数据连续排列
    const sortedData = data.sort((a, b) => {
      // 首先按项目名称排序
      if (a.projectName !== b.projectName) {
        return a.projectName.localeCompare(b.projectName)
      }
      // 相同项目内按成员姓名排序
      return a.memberName.localeCompare(b.memberName)
    })

    departmentPerformanceData.value = sortedData

    // 清空选择
    selectedDepartmentData.value = []
  } catch (error) {
    console.error('获取部门绩效表确认数据失败:', error)
    message.error('获取数据失败，请稍后重试')
  } finally {
    departmentLoading.value = false
  }
}

/** 部门绩效表确认查询按钮操作 */
const handleDepartmentQuery = async () => {
  await loadDepartmentPerformanceData()
}

/** 部门绩效表确认重置按钮操作 */
const handleDepartmentReset = async () => {
  departmentFilterParams.monthPeriod = getCurrentMonth()

  // 权限控制：部门经理重置时保持其默认部门
  const nickname = userStore.user.nickname
  if (nickname === '超级管理员') {
    departmentFilterParams.managingDepartment = ''
  } else if (allowedDepartments.value.length === 1) {
    // 如果只有一个部门权限，保持默认值
    departmentFilterParams.managingDepartment = allowedDepartments.value[0]
  } else {
    departmentFilterParams.managingDepartment = ''
  }

  await loadDepartmentPerformanceData()
}

/** 页签切换处理 */
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  // 切换页签时清空选择
  selectedDepartmentData.value = []
  selectedRejectedData.value = []
  // 重新加载数据
  loadDepartmentPerformanceData()
}

/** 监听主办部门变化，自动查询 */
watch(
  () => departmentFilterParams.managingDepartment,
  (newValue, oldValue) => {
    // 只有在弹窗打开状态下才自动查询，避免初始化时的误触发
    if (departmentPerformanceDialogVisible.value && newValue !== oldValue) {
      loadDepartmentPerformanceData()
    }
  }
)

// 防止死循环的标记
const isUpdatingSelection = ref(false)

/** 处理表格选择变化 */
const handleSelectionChange = (selection: DepartmentPerformanceVO[]) => {
  // 如果正在更新选择状态，只更新数据不进行联动
  if (isUpdatingSelection.value) {
    selectedDepartmentData.value = selection
    return
  }

  // 获取之前和当前的项目选择状态
  const previousProjects = new Set(selectedDepartmentData.value.map(item => item.projectName))
  const currentProjects = new Set(selection.map(item => item.projectName))

  // 分析变化：找出新增和移除的项目
  const addedProjects = new Set([...currentProjects].filter(p => !previousProjects.has(p)))
  const removedProjects = new Set([...previousProjects].filter(p => !currentProjects.has(p)))

  // 确定最终应该选中的项目集合
  let finalProjects = new Set(previousProjects)

  // 处理新增的项目
  addedProjects.forEach(project => {
    finalProjects.add(project)
  })

  // 处理移除的项目
  removedProjects.forEach(project => {
    finalProjects.delete(project)
  })

  // 检查是否有部分选择的项目（用户可能点击了某个项目的一行）
  const partiallySelectedProjects = new Set<string>()

  // 统计每个项目在当前selection中的选择情况
  const projectSelectionCount = new Map<string, number>()
  const projectTotalCount = new Map<string, number>()

  // 统计每个项目的总数
  departmentPerformanceData.value.forEach(item => {
    projectTotalCount.set(item.projectName, (projectTotalCount.get(item.projectName) || 0) + 1)
  })

  // 统计选中数
  selection.forEach(item => {
    projectSelectionCount.set(item.projectName, (projectSelectionCount.get(item.projectName) || 0) + 1)
  })

  // 找出部分选择的项目
  projectTotalCount.forEach((total, projectName) => {
    const selected = projectSelectionCount.get(projectName) || 0
    if (selected > 0 && selected < total) {
      partiallySelectedProjects.add(projectName)
    }
  })

  // 处理部分选择的项目：如果之前没选中，现在选中；如果之前选中了，现在取消选中
  partiallySelectedProjects.forEach(projectName => {
    if (previousProjects.has(projectName)) {
      // 之前选中了，现在取消选中
      finalProjects.delete(projectName)
    } else {
      // 之前没选中，现在选中
      finalProjects.add(projectName)
    }
  })

  // 构建最终的选择列表
  const completeSelection: DepartmentPerformanceVO[] = []
  departmentPerformanceData.value.forEach(item => {
    if (finalProjects.has(item.projectName)) {
      completeSelection.push(item)
    }
  })

  // 检查是否需要更新表格状态
  const needUpdate = completeSelection.length !== selection.length ||
    !completeSelection.every(item => selection.find(s => s === item)) ||
    !selection.every(item => completeSelection.find(c => c === item))

  if (needUpdate) {
    // 需要更新表格选择状态以保持项目级别的完整性
    isUpdatingSelection.value = true

    // 使用nextTick确保DOM更新完成
    nextTick(() => {
      tableRef.value?.clearSelection()
      completeSelection.forEach(item => {
        tableRef.value?.toggleRowSelection(item, true)
      })

      // 延迟重置标记
      setTimeout(() => {
        isUpdatingSelection.value = false
      }, 50)
    })
  }

  selectedDepartmentData.value = completeSelection
}

/** 项目级别的统计计算 */
const selectedProjectsCount = computed(() => {
  const selectedProjects = new Set(selectedDepartmentData.value.map(item => item.projectName))
  return selectedProjects.size
})

const totalProjectsCount = computed(() => {
  const allProjects = new Set(departmentPerformanceData.value.map(item => item.projectName))
  return allProjects.size
})

const isAllProjectsSelected = computed(() => {
  return selectedProjectsCount.value === totalProjectsCount.value && totalProjectsCount.value > 0
})

/** 已驳回页签的项目级别统计计算 */
const selectedRejectedProjectsCount = computed(() => {
  const selectedProjects = new Set(selectedRejectedData.value.map(item => item.projectName))
  return selectedProjects.size
})

const isAllRejectedProjectsSelected = computed(() => {
  return selectedRejectedProjectsCount.value === totalProjectsCount.value && totalProjectsCount.value > 0
})

/** 全选/取消全选 */
const tableRef = ref()
const toggleSelectAll = () => {
  isUpdatingSelection.value = true

  if (isAllProjectsSelected.value) {
    // 当前是全选状态，执行取消全选
    tableRef.value?.clearSelection()
    selectedDepartmentData.value = []
  } else {
    // 当前不是全选状态，执行全选
    tableRef.value?.clearSelection()
    departmentPerformanceData.value.forEach(row => {
      tableRef.value?.toggleRowSelection(row, true)
    })
    selectedDepartmentData.value = [...departmentPerformanceData.value]
  }

  // 延迟重置标记
  setTimeout(() => {
    isUpdatingSelection.value = false
  }, 100)
}

/** 已驳回页签的选择处理 */
const rejectedTableRef = ref()
const isUpdatingRejectedSelection = ref(false)

/** 处理已驳回表格选择变化 */
const handleRejectedSelectionChange = (selection: DepartmentPerformanceVO[]) => {
  // 如果正在更新选择状态，只更新数据不进行联动
  if (isUpdatingRejectedSelection.value) {
    selectedRejectedData.value = selection
    return
  }

  // 获取之前和当前的项目选择状态
  const previousProjects = new Set(selectedRejectedData.value.map(item => item.projectName))
  const currentProjects = new Set(selection.map(item => item.projectName))

  // 分析变化：找出新增和移除的项目
  const addedProjects = new Set([...currentProjects].filter(p => !previousProjects.has(p)))
  const removedProjects = new Set([...previousProjects].filter(p => !currentProjects.has(p)))

  // 确定最终应该选中的项目集合
  let finalProjects = new Set(previousProjects)

  // 处理新增的项目
  addedProjects.forEach(project => {
    finalProjects.add(project)
  })

  // 处理移除的项目
  removedProjects.forEach(project => {
    finalProjects.delete(project)
  })

  // 检查是否有部分选择的项目
  const partiallySelectedProjects = new Set<string>()

  // 统计每个项目在当前selection中的选择情况
  const projectSelectionCount = new Map<string, number>()
  const projectTotalCount = new Map<string, number>()

  // 统计每个项目的总数
  departmentPerformanceData.value.forEach(item => {
    projectTotalCount.set(item.projectName, (projectTotalCount.get(item.projectName) || 0) + 1)
  })

  // 统计选中数
  selection.forEach(item => {
    projectSelectionCount.set(item.projectName, (projectSelectionCount.get(item.projectName) || 0) + 1)
  })

  // 找出部分选择的项目
  projectTotalCount.forEach((total, projectName) => {
    const selected = projectSelectionCount.get(projectName) || 0
    if (selected > 0 && selected < total) {
      partiallySelectedProjects.add(projectName)
    }
  })

  // 处理部分选择的项目
  partiallySelectedProjects.forEach(projectName => {
    if (previousProjects.has(projectName)) {
      finalProjects.delete(projectName)
    } else {
      finalProjects.add(projectName)
    }
  })

  // 构建最终的选择列表
  const completeSelection: DepartmentPerformanceVO[] = []
  departmentPerformanceData.value.forEach(item => {
    if (finalProjects.has(item.projectName)) {
      completeSelection.push(item)
    }
  })

  // 检查是否需要更新表格状态
  const needUpdate = completeSelection.length !== selection.length ||
    !completeSelection.every(item => selection.find(s => s === item)) ||
    !selection.every(item => completeSelection.find(c => c === item))

  if (needUpdate) {
    isUpdatingRejectedSelection.value = true

    nextTick(() => {
      rejectedTableRef.value?.clearSelection()
      completeSelection.forEach(item => {
        rejectedTableRef.value?.toggleRowSelection(item, true)
      })

      setTimeout(() => {
        isUpdatingRejectedSelection.value = false
      }, 50)
    })
  }

  selectedRejectedData.value = completeSelection
}

/** 已驳回页签全选/取消全选 */
const toggleSelectAllRejected = () => {
  isUpdatingRejectedSelection.value = true

  if (isAllRejectedProjectsSelected.value) {
    // 当前是全选状态，执行取消全选
    rejectedTableRef.value?.clearSelection()
    selectedRejectedData.value = []
  } else {
    // 当前不是全选状态，执行全选
    rejectedTableRef.value?.clearSelection()
    departmentPerformanceData.value.forEach(row => {
      rejectedTableRef.value?.toggleRowSelection(row, true)
    })
    selectedRejectedData.value = [...departmentPerformanceData.value]
  }

  setTimeout(() => {
    isUpdatingRejectedSelection.value = false
  }, 100)
}

/** 确认本部门本月度绩效占比按钮操作 */
const handleConfirmDepartmentPerformance = async () => {
  try {
    await message.confirm(`确定要确认选中的 ${selectedProjectsCount.value} 个项目的绩效占比数据吗？确认后将无法修改！`, '提示')

    confirmDepartmentLoading.value = true

    // 调用后端接口确认绩效占比（只处理勾选的数据）
    await JixiaoApi.confirmDepartmentPerformance({
      monthPeriod: departmentFilterParams.monthPeriod,
      managingDepartment: departmentFilterParams.managingDepartment,
      data: selectedDepartmentData.value
    })

    message.success('确认成功！绩效占比已提交')

    // 关闭弹窗
    departmentPerformanceDialogVisible.value = false

  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认绩效占比失败:', error)
      message.error('确认失败，请稍后重试')
    }
  } finally {
    confirmDepartmentLoading.value = false
  }
}


/** 驳回绩效表按钮操作 */
const handleRejectDepartmentPerformance = async () => {
  try {
    if (selectedProjectsCount.value === 0) {
      message.error('请先选择要驳回的项目')
      return
    }

    // 重置驳回表单
    rejectForm.reason = ''

    // 显示驳回原因输入弹窗
    rejectReasonDialogVisible.value = true

  } catch (error) {
    console.error('打开驳回弹窗失败:', error)
    message.error('操作失败，请稍后重试')
  }
}

/** 确认驳回操作 */
const handleConfirmReject = async () => {
  try {
    // 验证表单
    await rejectFormRef.value.validate()

    rejectDepartmentLoading.value = true
    // 从选中的数据中获取主办部门（所有选中数据的主办部门应该是一致的）
    const managingDepartment = selectedDepartmentData.value.length > 0
      ? selectedDepartmentData.value[0].managingDepartment
      : departmentFilterParams.managingDepartment

    // 调用后端接口驳回绩效占比
    await JixiaoApi.rejectDepartmentPerformance({
      monthPeriod: departmentFilterParams.monthPeriod,
      managingDepartment: managingDepartment,
      rejectReason: rejectForm.reason,
      data: selectedDepartmentData.value
    })

    message.success(`驳回成功！已驳回 ${selectedProjectsCount.value} 个项目的绩效占比数据`)

    // 关闭驳回原因弹窗
    rejectReasonDialogVisible.value = false
    // 刷新数据，不关闭主弹窗
    await loadDepartmentPerformanceData()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('驳回绩效占比失败:', error)
      message.error('驳回失败，请稍后重试')
    }
  } finally {
    rejectDepartmentLoading.value = false
  }
}

/** 撤销驳回绩效表按钮操作 */
const handleRevokeRejectDepartmentPerformance = async () => {
  try {
    if (selectedRejectedProjectsCount.value === 0) {
      message.error('请先选择要撤销驳回的项目')
      return
    }

    await message.confirm(`确定要撤销驳回选中的 ${selectedRejectedProjectsCount.value} 个项目吗？撤销后，这些项目将重新回到待确认状态！`, '提示')

    revokeRejectLoading.value = true

    // 从选中的数据中获取主办部门
    const managingDepartment = selectedRejectedData.value.length > 0
      ? selectedRejectedData.value[0].managingDepartment
      : departmentFilterParams.managingDepartment

    // 调用后端接口撤销驳回绩效占比
    await JixiaoApi.revokeRejectDepartmentPerformance({
      monthPeriod: departmentFilterParams.monthPeriod,
      managingDepartment: managingDepartment,
      data: selectedRejectedData.value
    })

    message.success(`撤销驳回成功！已将 ${selectedRejectedProjectsCount.value} 个项目重新设为待确认状态`)

    // 刷新数据，不关闭主弹窗
    await loadDepartmentPerformanceData()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销驳回绩效占比失败:', error)
      message.error('撤销驳回失败，请稍后重试')
    }
  } finally {
    revokeRejectLoading.value = false
  }
}


/** 在新窗口中打开绩效表 */
const openInNewWindow = () => {
  if (performanceUrl.value) {
    window.open(performanceUrl.value, '_blank')
    message.success('已在新窗口中打开绩效表')
  }
}

/** 监听弹窗状态变化，控制页面滚动 */
watch(performanceDialogVisible, (newVal) => {
  if (newVal) {
    // 弹窗打开时，禁用页面滚动
    document.body.style.overflow = 'hidden'
    // 添加事件监听器阻止滚动事件冒泡
    document.addEventListener('wheel', preventScroll, { passive: false })
    document.addEventListener('touchmove', preventScroll, { passive: false })
  } else {
    // 弹窗关闭时，恢复页面滚动
    document.body.style.overflow = ''
    // 移除事件监听器
    document.removeEventListener('wheel', preventScroll)
    document.removeEventListener('touchmove', preventScroll)
  }
})

/** 阻止滚动事件的函数 */
const preventScroll = (e: Event) => {
  // 检查事件目标是否在iframe容器内
  const target = e.target as Element
  const iframeContainer = document.querySelector('.performance-iframe-container')

  if (iframeContainer && !iframeContainer.contains(target)) {
    e.preventDefault()
    e.stopPropagation()
  }
}

/** 组件卸载时清理事件监听器 */
onUnmounted(() => {
  document.body.style.overflow = ''
  document.removeEventListener('wheel', preventScroll)
  document.removeEventListener('touchmove', preventScroll)
})

/** 初始化 **/
onMounted(() => {
  getList()
})

// 需要合并的字段
const mergeFields = [
  'contractId', 'projectId', 'projectName', 'projectNature', 'projectType', 'projectCoefficient',
  'contractWorkday', 'projectWorkday', 'personNum', 'projectManage',
  'seasonWorkday', 'seasonAllocate', 'annualWorkday', 'annualAllocate',
  'exWorkday', 'deductions', 'remarks', 'yearmonth'
];

// 不合并的明细字段
const detailFields = [
  'projectPerson', 'projectProportions', 'seasonPersonallocate', 'annualPersonallocate'
];

// 部门绩效表确认需要合并的字段
const departmentMergeFields = [
  'monthPeriod', 'projectName', 'projectNature', 'managingDepartment',
  'projectType', 'type', 'projectCoefficient', 'projectManagerName',
  'contractDuration', 'memberCount', 'importance', 'totalAmount'
];

// 部门绩效表确认不合并的明细字段
const departmentDetailFields = [
  'memberName', 'personalPercentage'
];

// 计算每组的起始行和行数
const rowSpans = computed(() => {
  const map = {};
  list.value.forEach((row, index) => {
    const key = `${row.contractId}_${row.projectId}_${row.yearmonth}`;
    if (!map[key]) {
      map[key] = { start: index, count: 1 };
    } else {
      map[key].count += 1;
    }
  });
  return map;
});

// 计算部门绩效表确认每组的起始行和行数
const departmentRowSpans = computed(() => {
  const map = {};
  departmentPerformanceData.value.forEach((row, index) => {
    // 按项目名称分组，这样同一个项目的多个成员会被合并
    const key = row.projectName;
    if (!map[key]) {
      map[key] = { start: index, count: 1 };
    } else {
      map[key].count += 1;
    }
  });

  return map;
});

// 新增月期独立的分组计算
const monthRowSpans = computed(() => {
  const map = {};
  list.value.forEach((row, index) => {
    const key = row.yearmonth; // 仅以月期为分组依据
    if (!map[key]) {
      map[key] = { start: index, count: 1 };
    } else {
      map[key].count += 1;
    }
  });
  return map;
});

function mergeCells({ row, column, rowIndex }) {
  const field = column.property;
  // 处理月期列的独立合并
  if (field === 'yearmonth') {
    const group = monthRowSpans.value[row.yearmonth];
    if (group && group.start === rowIndex) {
      return [group.count, 1]; // 合并相同月期的单元格
    }
    return [0, 0]; // 非首行隐藏
  }
  // 其他列保持原有合并逻辑
  if (mergeFields.includes(field)) {
    const key = `${row.contractId}_${row.projectId}_${row.yearmonth}`;
    const group = rowSpans.value[key];
    if (group && group.start === rowIndex) {
      return [group.count, 1];
    }
    return [0, 0];
  }
  return [1, 1];
}


// 部门绩效表确认合并函数
function departmentMergeCells({ row, column, rowIndex }) {
  const field = column.property;
  if (departmentMergeFields.includes(field)) {
    // 找到当前分组 - 按项目名称分组
    const key = row.projectName;
    const group = departmentRowSpans.value[key];

    if (group && group.start === rowIndex) {
      // 组的第一行，合并
      return [group.count, 1];
    } else {
      // 其他行隐藏
      return [0, 0];
    }
  }
  // 明细字段不合并
  return [1, 1];
}
</script>

<style scoped>
/* 弹窗样式 */
:deep(.performance-dialog) {
  .el-dialog__body {
    padding: 10px;
    overflow: hidden;
  }

  .el-dialog__header {
    padding: 15px 20px 10px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
  }
}

/* iframe容器样式 */
.performance-iframe-container {
  height: 80vh;
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  /* 阻止滚动事件冒泡 */
  overscroll-behavior: contain;
  /* 确保容器本身不滚动 */
  scroll-behavior: auto;
}

/* iframe样式 */
.performance-iframe-container iframe {
  display: block;
  /* 确保iframe完全填充容器 */
  border: none;
  outline: none;
  /* 防止iframe影响父页面滚动 */
  pointer-events: auto;
}

/* 加载占位符样式 */
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
  background-color: #fafafa;
}

.loading-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 防止弹窗打开时页面滚动 */
:deep(.el-dialog__wrapper) {
  overflow: hidden;
}

/* 确保弹窗内容区域的滚动独立 */
:deep(.el-dialog) {
  margin: 5vh auto;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: hidden;
  padding: 10px;
}

/* 部门绩效表确认弹窗样式 */
:deep(.department-performance-dialog) {
  .el-dialog__body {
    padding: 20px;
    overflow: hidden;
  }

  .el-dialog__header {
    padding: 20px 20px 10px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}

/* 弹窗内容居中布局 */
.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.filter-form {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.table-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.table-container .el-table {
  max-width: 100%;
}

/* 勾选统计信息样式 */
.selection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #606266;
}

.selection-info strong {
  color: #409eff;
  font-weight: 600;
}

/* 部门绩效表确认按钮样式优化 */
.performance-confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.performance-confirm-btn:hover {
  background: linear-gradient(135deg, #337ecc 0%, #529b2e 100%);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.performance-confirm-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.performance-confirm-btn.is-loading {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  opacity: 0.8;
}
/* 部门绩效表确认按钮样式优化 */
.performance-confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.performance-confirm-btn:hover {
  background: linear-gradient(135deg, #337ecc 0%, #529b2e 100%);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.performance-confirm-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.performance-confirm-btn.is-loading {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  opacity: 0.8;
}

->

   /* 部门绩效表确认按钮样式优化 */
 .performance-confirm-btn {
   background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
   border: none;
   box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
   transition: all 0.3s ease;
   font-weight: 500;
 }

.performance-confirm-btn:hover {
  background: linear-gradient(135deg, #337ecc 0%, #529b2e 100%);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.performance-confirm-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.performance-confirm-btn.is-loading {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  opacity: 0.8;
}

/* 驳回弹窗样式 */
:deep(.reject-dialog) {
  .el-dialog__header {
    background-color: #fef0f0;
    border-bottom: 1px solid #fde2e2;
  }

  .el-dialog__title {
    color: #f56c6c;
    font-weight: 600;
  }
}

.reject-content {
  padding: 10px 0;
}

.reject-info {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
  border-radius: 6px;
  margin-bottom: 20px;
}

.reject-icon {
  font-size: 20px;
  color: #f56c6c;
  margin-right: 10px;
  margin-top: 2px;
}

.reject-text p {
  margin: 0;
  line-height: 1.5;
}

.reject-text p:first-child {
  color: #f56c6c;
  margin-bottom: 5px;
}

.reject-desc {
  color: #909399;
  font-size: 13px;
}

/* 页签样式 */
.performance-tabs {
  margin-top: 10px;
}

.performance-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.performance-tabs .el-tabs__item {
  font-size: 16px;
  font-weight: 500;
}

.performance-tabs .el-tabs__item.is-active {
  color: #409EFF;
}

.tab-content {
  min-height: 400px;
}

.department-performance-dialog .el-dialog__body {
  padding: 10px 20px 20px;
}

.filter-form {
  margin-bottom: 10px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.selection-info {
  margin-bottom: 15px;
  padding: 10px 15px;
  background-color: #e7f3ff;
  border-left: 4px solid #409EFF;
  border-radius: 4px;
}

.selection-info strong {
  color: #409EFF;
}
</style>
