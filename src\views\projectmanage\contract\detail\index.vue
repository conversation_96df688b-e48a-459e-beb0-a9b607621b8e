<template>
  <ContentWrap>
    <div class="main-contract-title" v-if="one.patchId != null">主合同信息</div>
    <div class="main-contract-title" v-else>合同信息</div>
    <el-form v-loading="loading" :label-position="'left'" label-width="100px" :model="one"
             :rules="oneRules" ref="formRef" @change="handleAutoSave">
      <!--      {{ one }}-->
      <el-button @click="submitForm" type="primary" v-if="route.params.contractId === '0'">新建保存</el-button>
      <el-button @click="submitForm" type="primary" v-else>修改保存</el-button>
      <el-button @click="patchForm" type="primary" v-if="one.patchId == null && one.mainId == null">补充合同信息</el-button>
      <el-button @click="returnXM" type="primary" >返回项目详情</el-button>
      <el-button @click="closeWorkdayReminder" type="primary" v-if="one.ifWorkday == '是'">关闭工期提醒</el-button>
      <el-button @click="closeFuwuServiceReminder" type="primary" v-if="one.ifFuwuService == '是'">关闭服务提醒</el-button>
      <el-button @click="closeWarrantyTimeReminder" type="primary" v-if="one.ifWarrantyTime == '是'">关闭质保金提醒</el-button>
      <P/>

      <el-row>
        <el-col :span="5">
          <el-form-item label="合同名称" prop="contractName" class="w-90%">
            <el-input v-model="one.contractName" placeholder="请输入合同名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="合同编号" prop="contractCode" class="w-90%">
            <el-input v-model="one.contractCode" placeholder="请输入合同编号"/>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="合作单位" prop="cooperationUnit" class="w-90%">
            <el-select
              v-model="cooperationUnit"
              placeholder="请选择合作单位"
              clearable
              filterable
              :loading="loading"
            >
              <el-option
                v-for="option in nameOptions"
                :key="option.id"
                :label="option.name"
                :value="option.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="别名" prop="otherName" class="w-90%">
            <el-input v-model="one.otherName" placeholder="请输入合同名称"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="5">
          <el-form-item label="合作类型" prop="paymentRelationship" class="w-90%">
            <el-select v-model="one.paymentRelationship" placeholder="请选择币种">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PAYMENT_RELATION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="5"  v-if="sourceType">
          <el-form-item label="来源方式" prop="sourceMethods" class="w-90%">
            <el-select v-model="one.sourceMethods" placeholder="请选择来源方式" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SOURCE_METHODS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item label="主办部门" prop="executingDeparment" class="!w-90%">
            <el-select v-model="one.executingDeparment" placeholder="请选择履行部门" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.MANAGING_DEPARTMENT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="财务类别" prop="financialClassification" class="w-90%">
            <el-select v-model="one.financialClassification" placeholder="请选择财务类别" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.FINANCIAL_CLASSIFICATION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="项目类别" prop="projecttype" class="w-90%" >
            <el-input v-model="one.projecttype" placeholder="请输入项目类别" disabled/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>


        <el-col :span="5">
          <el-form-item label="年份" prop="contractYear" class="!w-90%">
            <el-date-picker
              v-model="one.contractYear"
              type="year"
              placeholder="选择年"
              value-format="YYYY"
            />
          </el-form-item>
        </el-col>
<!--        <el-col :span="5">-->
<!--          <el-form-item label="业务合同编码" prop="businessContractCode" class="!w-90%">-->
<!--            <el-input v-model="one.businessContractCode" placeholder="请输入业务合同编码"/>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="5">
          <el-form-item label="状态" prop="status" class="w-90%">
            <el-select v-model="one.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.CONTRACT_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
            <el-form-item label="经办人" prop="handler" class="w-90%">
            <el-input v-model="one.handler" placeholder="请输入经办人"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-button @click="handleAddRateAndAmount" type="primary"
                     style="margin-top: 10px;margin-left: 0px;">&nbsp;新增税率和金额
          </el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="金额情况" prop="rate">
            <div>
              <div v-for="(item, index) in one.ratesAndAmounts" :key="index" style="margin-top: 10px;">
                <el-row :gutter="30">
                  <el-col :span="5">
                    <el-form-item label="税率">
                      <el-select v-model="item.rate" placeholder="请选择税率">
                        <el-option
                          v-for="dict in getStrDictOptions(DICT_TYPE.CONTRACT_TAXRATE)"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item label="含税金额">
                      <el-input v-model="item.amount" placeholder="含税金额" style="width: 100%;"/>
                    </el-form-item>
                  </el-col>

                  <el-col :span="5">
                    <el-form-item label="不含税金额">
                      <el-input
                        :value="(item.amount/(1+item.rate/100)).toFixed(2)"
                        placeholder="不含税金额" readonly style="width: 100%;"/>
                    </el-form-item>
                  </el-col>

                  <el-col :span="5">
                    <el-form-item label="税额">
                      <el-input readonly
                                :value="(item.amount/(1+item.rate/100)*(item.rate/100)).toFixed(2)"
                                placeholder="税额" style="width: 100%;"/>
                    </el-form-item>
                  </el-col>

                  <el-col :span="2">
                    <el-button @click="handleRemoveRateAndAmount(index)" type="danger" plain>删除
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-form-item label="含税总金额" prop="amount" class="w-65%">
            <div style="font-weight: bold; color: black;">{{
                money_format(one.amount, 2, '.', ',')
              }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item label="不含税总金额" prop="amount" class="w-65%">
            <div style="font-weight: bold; color: black;">
              {{ money_format(getNoTaxPrice, 2, '.', ',') }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item label="总税额" prop="amount" class="w-65%">
            <div style="font-weight: bold; color: black;">{{
                money_format(getTaxPrice, 2, '.', ',')
              }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="15">
          <el-form-item label="合同内容概要" prop="remark">
            <el-input type="textarea" v-model="one.remark" placeholder="请输入合同内容概要"/>
          </el-form-item>
        </el-col>
      </el-row>


      <!--      <el-row>-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="项目名称" prop="projectName" class="w-65%">-->
      <!--            <el-input v-model="one.projectName" placeholder="请输入项目名称" readonly/>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="项目编号" prop="projectCode" class="w-65%">-->
      <!--            <el-input v-model="one.projectCode" placeholder="请输入项目编号" readonly/>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->


      <!--        //隐藏甲乙双方id-->
      <!--        <el-row>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="甲方ID" prop="jiafangId">-->
      <!--              <el-input v-model="one.jiafangId" placeholder="请输入甲方ID" disabled/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="乙方ID" prop="yifangId" >-->
      <!--              <el-input v-model="one.yifangId" placeholder="请输入乙方ID" disabled/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->

      <!--        <el-row>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="甲方签订人" prop="jiafangSignatory" class="w-65%">-->
      <!--              <el-input v-model="one.jiafangSignatory" placeholder="请输入甲方签订人"/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="乙方签订人" prop="yifangSignatory" class="w-65%">-->
      <!--              <el-input v-model="one.yifangSignatory" placeholder="请输入乙方签订人"/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->

<!--      <el-row>-->
<!--        <el-col :span="6">-->
<!--          <el-form-item label="签订时间" prop="signingTime" class="w-75%">-->
<!--            <el-date-picker-->
<!--              v-model="one.signingTime"-->
<!--              type="date"-->
<!--              value-format="YYYY-MM-DD"-->
<!--              placeholder="请选择签订时间"-->
<!--              style="width: 100%"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="6">-->
<!--          <el-form-item label="工期(天)" prop="workDay" class="w-75%">-->
<!--            <el-input-->
<!--              type="number"-->
<!--              v-model="one.workDay"-->
<!--              placeholder="请输入工期"-->
<!--              @input="workDayInput"-->
<!--              style="width: 100%"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="6">-->
<!--          <el-form-item label="完工时间" prop="endTime" class="w-75%">-->
<!--            <el-date-picker-->
<!--              v-model="one.endTime"-->
<!--              type="date"-->
<!--              placeholder="请选择完工时间"-->
<!--              style="width: 100%"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <el-row>
        <el-col :span="6">
          <el-form-item label="质保/服务期(年)" prop="warrantyService" class="w-75%" label-width="120px">
            <el-select v-model="one.warrantyService" placeholder="请选择质保期" style="width: 100%">
              <el-option
                v-for="year in [0, 1, 2, 3, 4, 5]"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否有质保金" prop="isWarranty" class="w-75%">
            <el-select v-model="one.isWarranty" placeholder="请选择是否有质保金" clearable>
              <el-option label="是" value="是"/>
              <el-option label="否" value="否"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="质保金应收时间" prop="warrantyDueTime" class="el-form-item__label, w-75%">
            <el-date-picker
              v-model="one.warrantyDueTime"
              type="date"
              placeholder="请选择质保金应收时间"
              value-format="YYYY-MM-DD"
              :disabled-date="(time) => time.getTime() < Date.now()"
              :disabled="one.isWarranty === '否'"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- <el-col :span="6">
          <el-form-item label="质保金" prop="warrantyAmount">
            <el-input
              type="number"
              v-model="one.warrantyAmount"
              placeholder="请输入质保金金额"
              style="width: 100%"
            >
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col> -->

      </el-row>

      <!-- 删除原来的备注行 -->




      <el-button type="primary" @click="addRow" v-if="one.paymentRelationship=='收'">
        新增结算信息（收）
      </el-button>


      <el-button type="primary" @click="addRow" v-else>新增结算信息（付）</el-button>
      <!--        已分配比例：{{ totalProportion }}%   已分配金额：{{ money_format(feeListtotalAmount, 2, '.', ',') }}  合同金额-分配金额：{{ money_format(feeListtotalAmount-one.amount, 2, '.', ',') }}-->


      <div class="allocation-info">
        已分配比例：<span>{{ totalProportion }}%</span>
        已分配金额：<span>{{ money_format(feeListtotalAmount, 2, '.', ',') }}</span>
        合同金额-分配金额：<span>{{
          money_format(one.amount - feeListtotalAmount, 2, '.', ',')
        }}</span>
      </div>
      <el-table v-loading="loading" :data="feeList" :show-overflow-tooltip="true"
                :row-class-name="tableRowClassName">
        <el-table-column label="序列">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="款项类型">
          <template #default="scope">
            <!--              <el-input v-model="scope.row.paymentType"/>-->

            <el-select v-if="one.paymentRelationship=='付'" v-model="scope.row.type"
                       placeholder="请选择款项类型" clearable>
              <el-option
                v-for="dict in getStrDictOptions(`pay_fee_type`)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

            <el-select v-if="one.paymentRelationship=='收'" v-model="scope.row.type"
                       placeholder="请选择款项类型" clearable>
              <el-option
                v-for="dict in getStrDictOptions(`fee_type`)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

          </template>
        </el-table-column>

        <el-table-column label="占比（%）">
          <template #default="scope">
            <el-input type="number" max="100" min="1" v-model="scope.row.proportion"
                      @input="proportionChange(scope.row)" @blur="proportionBlur(scope.row)"/>
          </template>
        </el-table-column>

        <el-table-column label="含税金额（元）">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.amount"
                      @blur="getTaxAndNoTaxPrice(scope.row)"/>
          </template>
        </el-table-column>
        <el-table-column label="款项说明">
          <template #default="scope">
            <el-input v-model="scope.row.remarks"/>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button @click="handleRemoveFee(scope.$index)" type="danger" plain>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row v-for="group in groupFileList" :key="group[0].fileBusinessTypeDetail"  class="file-upload-section">
        <el-col :span="24" >
          <!--            <el-card>-->
          <div>
            <span>{{ group[0].fileBusinessTypeDetail }}</span>
          </div>
          <!--group是个数组，每个对象里面有个url,  获取所有的url, 然后v-modle=所有的url,modelValue是url数组 -->
          <UploadFile :modelValue="collectedUrls(group)"
                      :businessId="one.id||0"
                      :fileBusinessType="`合同`"
                      :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
                      :url2Name="collectUrl2NameMap(group)"
                      :businessFileList="group"
          />
          <!--            </el-card>-->
        </el-col>
      </el-row>

    </el-form>
    <ElDialog v-model="dialogVisible" title="补充合同信息" width="90%">
      <ContractPatch :modelValue="dialogVisible" @update:model-value="handleDialogClose" />
    </ElDialog>
  </ContentWrap>
  <ContentWrap>
    <el-divider content-position="left" style="margin-left: 10px;margin-right: 10px">合同审批情况
    </el-divider>
    <el-button @click="dicontract" type="primary" v-if="showMultiDepartmentButton" >多部门分配表</el-button>
    <el-button @click="tuisong" type="primary" >推送OA审批</el-button>
    <el-button @click="oarouter" type="primary" >跳转OA界面</el-button>
    <el-button @click="oadelete" type="primary" >废弃OA审批</el-button>
<!--    <el-button @click="sanheyi" type="primary" >保存并推送OA审批</el-button>-->
    <OAendorse ref="OAendorseRef" :executingDeparment="one.executingDeparment" :contractId="route.params.contractId" :handle="one.handler" :projectId="one.projectId"/>
  </ContentWrap>

  <ContentWrap v-if="one.patchId != null">
    <div class="patch-contract-title">补充合同信息</div>
    <el-form v-loading="loading" :label-position="'left'" label-width="100px" :model="patch"
             :rules="oneRules" ref="formRef">
      <!--      {{ patch }}-->
      <el-button @click="patchsubmitForm" type="primary" >补充合同修改保存</el-button>
      <P/>

      <el-row>
        <el-col :span="5">
          <el-form-item label="合同名称" prop="contractName" class="w-90%">
            <el-input v-model="patch.contractName" placeholder="请输入合同名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="合同编号" prop="contractCode" class="w-90%">
            <el-input v-model="patch.contractCode" placeholder="请输入合同编号"/>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="合作单位" prop="cooperationUnit" class="w-90%">
            <el-select
              v-model="patchcooperationUnit"
              placeholder="请选择合作单位"
              clearable
              filterable
              :loading="loading"
            >
              <el-option
                v-for="option in nameOptions"
                :key="option.id"
                :label="option.name"
                :value="option.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="别名" prop="otherName" class="w-90%">
            <el-input v-model="patch.otherName" placeholder="请输入合同名称"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="5">
          <el-form-item label="合作类型" prop="paymentRelationship" class="w-90%">
            <el-select v-model="patch.paymentRelationship" placeholder="请选择币种">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PAYMENT_RELATION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="5"  v-if="sourceType">
          <el-form-item label="来源方式" prop="sourceMethods" class="w-90%">
            <el-select v-model="patch.sourceMethods" placeholder="请选择来源方式" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SOURCE_METHODS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item label="主办部门" prop="executingDeparment" class="!w-90%">
            <el-select v-model="patch.executingDeparment" placeholder="请选择履行部门" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.MANAGING_DEPARTMENT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="财务类别" prop="financialClassification" class="w-90%">
            <el-select v-model="patch.financialClassification" placeholder="请选择财务类别" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.FINANCIAL_CLASSIFICATION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>


        <el-col :span="5">
          <el-form-item label="年份" prop="contractYear" class="!w-90%">
            <el-date-picker
              v-model="patch.contractYear"
              type="year"
              placeholder="选择年"
              value-format="YYYY"
            />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="5">-->
        <!--          <el-form-item label="业务合同编码" prop="businessContractCode" class="!w-90%">-->
        <!--            <el-input v-model="patch.businessContractCode" placeholder="请输入业务合同编码"/>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="5">
          <el-form-item label="状态" prop="status" class="w-90%">
            <el-select v-model="patch.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.CONTRACT_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="经办人" prop="handler" class="w-90%">
            <el-input v-model="patch.handler" placeholder="请输入经办人"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="金额情况" prop="rate">
            <div v-for="(item, index) in patch.ratesAndAmounts" :key="index"
                 style="margin-top: 10px;">
              <el-row :gutter="30">
                <el-col :span="5">
                  <el-form-item label="税率">
                    <el-select v-model="item.rate" placeholder="请选择税率">
                      <el-option
                        v-for="dict in getStrDictOptions(DICT_TYPE.CONTRACT_TAXRATE)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="含税金额">
                    <el-input v-model="item.amount" placeholder="含税金额" style="width: 100%;"/>
                  </el-form-item>
                </el-col>

                <el-col :span="5">
                  <el-form-item label="不含税金额">
                    <el-input
                      :value="(item.amount/(1+item.rate/100)).toFixed(2)"
                      placeholder="不含税金额" readonly style="width: 100%;"/>
                  </el-form-item>
                </el-col>

                <el-col :span="5">
                  <el-form-item label="税额">
                    <el-input readonly
                              :value="(item.amount/(1+item.rate/100)*(item.rate/100)).toFixed(2)"
                              placeholder="税额" style="width: 100%;"/>
                  </el-form-item>
                </el-col>

                <el-col :span="2">
                  <el-button @click="patchhandleRemoveRateAndAmount(index)" type="danger" plain>删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <el-button @click="patchhandleAddRateAndAmount" type="primary" plain
                       style="margin-top: 10px;margin-left: 0px">新增税率和金额
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-form-item label="含税总金额" prop="amount" class="w-65%">
            <div style="font-weight: bold; color: black;">{{
                money_format(patch.amount, 2, '.', ',')
              }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item label="不含税总金额" prop="amount" class="w-65%">
            <div style="font-weight: bold; color: black;">
              {{ money_format(patchgetNoTaxPrice, 2, '.', ',') }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item label="总税额" prop="amount" class="w-65%">
            <div style="font-weight: bold; color: black;">{{
                money_format(patchgetTaxPrice, 2, '.', ',')
              }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="15">
          <el-form-item label="合同内容概要" prop="remark">
            <el-input type="textarea" v-model="patch.remark" placeholder="请输入合同内容概要"/>
          </el-form-item>
        </el-col>
      </el-row>


      <!--      <el-row>-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="项目名称" prop="projectName" class="w-65%">-->
      <!--            <el-input v-model="patch.projectName" placeholder="请输入项目名称" readonly/>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="项目编号" prop="projectCode" class="w-65%">-->
      <!--            <el-input v-model="patch.projectCode" placeholder="请输入项目编号" readonly/>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->


      <!--        //隐藏甲乙双方id-->
      <!--        <el-row>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="甲方ID" prop="jiafangId">-->
      <!--              <el-input v-model="patch.jiafangId" placeholder="请输入甲方ID" disabled/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="乙方ID" prop="yifangId" >-->
      <!--              <el-input v-model="patch.yifangId" placeholder="请输入乙方ID" disabled/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->

      <!--        <el-row>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="甲方签订人" prop="jiafangSignatory" class="w-65%">-->
      <!--              <el-input v-model="patch.jiafangSignatory" placeholder="请输入甲方签订人"/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="乙方签订人" prop="yifangSignatory" class="w-65%">-->
      <!--              <el-input v-model="patch.yifangSignatory" placeholder="请输入乙方签订人"/>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->

      <!--      <el-row>-->
      <!--        <el-col :span="6">-->
      <!--          <el-form-item label="签订时间" prop="signingTime" class="w-75%">-->
      <!--            <el-date-picker-->
      <!--              v-model="patch.signingTime"-->
      <!--              type="date"-->
      <!--              value-format="YYYY-MM-DD"-->
      <!--              placeholder="请选择签订时间"-->
      <!--              style="width: 100%"-->
      <!--            />-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--        <el-col :span="6">-->
      <!--          <el-form-item label="工期(天)" prop="workDay" class="w-75%">-->
      <!--            <el-input-->
      <!--              type="number"-->
      <!--              v-model="patch.workDay"-->
      <!--              placeholder="请输入工期"-->
      <!--              @input="workDayInput"-->
      <!--              style="width: 100%"-->
      <!--            />-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--        <el-col :span="6">-->
      <!--          <el-form-item label="完工时间" prop="endTime" class="w-75%">-->
      <!--            <el-date-picker-->
      <!--              v-model="patch.endTime"-->
      <!--              type="date"-->
      <!--              placeholder="请选择完工时间"-->
      <!--              style="width: 100%"-->
      <!--            />-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->

      <el-row>
        <el-col :span="6">
          <el-form-item label="质保/服务期（年）" prop="warrantyService" class="w-85%">
            <el-select v-model="patch.warrantyService" placeholder="请选择质保期" style="width: 100%">
              <el-option
                v-for="year in [0, 1, 2, 3, 4, 5]"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否有质保金" prop="isWarranty" class="w-75%">
            <el-select v-model="patch.isWarranty" placeholder="请选择是否有质保金" clearable>
              <el-option label="是" value="是"/>
              <el-option label="否" value="否"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="质保金应收时间" prop="warrantyDueTime" class="el-form-item__label, w-75%">
            <el-date-picker
              v-model="patch.warrantyDueTime"
              type="date"
              placeholder="请选择质保金应收时间"
              value-format="YYYY-MM-DD"
              :disabled-date="(time) => time.getTime() < Date.now()"
              :disabled="patch.isWarranty === '否'"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>


      <!--      <el-row>-->
      <!--        <el-col :span="6">-->
      <!--          <el-form-item label="审批状态" prop="approvalStatus"  class="!w-65%">-->
      <!--            <el-select v-model="patch.approvalStatus" placeholder="请选择审批状态" clearable-->
      <!--                      >-->
      <!--              <el-option-->
      <!--                v-for="dict in getStrDictOptions(DICT_TYPE.APPROVAL_STATUS)"-->
      <!--                :key="dict.value"-->
      <!--                :label="dict.label"-->
      <!--                :value="dict.value"-->
      <!--              />-->
      <!--            </el-select>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->


      <el-button type="primary" @click="addpatchRow" v-if="patch.paymentRelationship=='收'">
        新增结算信息（收）
      </el-button>


      <el-button type="primary" @click="addpatchRow" v-else>结算信息（付）</el-button>
      <!--        已分配比例：{{ totalProportion }}%   已分配金额：{{ money_format(feeListtotalAmount, 2, '.', ',') }}  合同金额-分配金额：{{ money_format(feeListtotalAmount-patch.amount, 2, '.', ',') }}-->


      <div class="allocation-info">
        已分配比例：<span>{{ totalProportion }}%</span>
        已分配金额：<span>{{ money_format(patchfeeListtotalAmount, 2, '.', ',') }}</span>
        合同金额-分配金额：<span>{{
          money_format(patch.amount - patchfeeListtotalAmount, 2, '.', ',')
        }}</span>
      </div>
      <el-table v-loading="loading" :data="patchfeeList" :show-overflow-tooltip="true"
                :row-class-name="tableRowClassName">
        <el-table-column label="序列">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="款项类型">
          <template #default="scope">
            <!--              <el-input v-model="scope.row.paymentType"/>-->

            <el-select v-if="patch.paymentRelationship=='付'" v-model="scope.row.type"
                       placeholder="请选择款项类型" clearable>
              <el-option
                v-for="dict in getStrDictOptions(`pay_fee_type`)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

            <el-select v-if="patch.paymentRelationship=='收'" v-model="scope.row.type"
                       placeholder="请选择款项类型" clearable>
              <el-option
                v-for="dict in getStrDictOptions(`fee_type`)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

          </template>
        </el-table-column>

        <el-table-column label="占比（%）">
          <template #default="scope">
            <el-input type="number" max="100" min="1" v-model="scope.row.proportion"
                      @input="proportionChange(scope.row)" @blur="patchproportionBlur(scope.row)"/>
          </template>
        </el-table-column>

        <el-table-column label="含税金额（元）">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.amount"
                      @blur="patchgetTaxAndNoTaxPrice(scope.row)"/>
          </template>
        </el-table-column>
        <el-table-column label="款项说明">
          <template #default="scope">
            <el-input v-model="scope.row.remarks"/>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button @click="patchhandleRemoveFee(scope.$index)" type="danger" plain>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row v-for="group in patchgroupFileList" :key="group[0].fileBusinessTypeDetail">
        <el-col :span="24">
          <!--            <el-card>-->
          <div>
            <span>{{ group[0].fileBusinessTypeDetail }}</span>
          </div>
          <!--group是个数组，每个对象里面有个url,  获取所有的url, 然后v-modle=所有的url,modelValue是url数组 -->
          <UploadFile :modelValue="collectedUrls(group)"
                      :businessId="patch.id||0"
                      :fileBusinessType="`合同`"
                      :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
                      :url2Name="collectUrl2NameMap(group)"
                      :businessFileList="group"
          />
          <!--            </el-card>-->
        </el-col>
      </el-row>

    </el-form>

    <ContentWrap>
      <el-divider content-position="left" style="margin-left: 10px;margin-right: 10px">合同审批情况
      </el-divider>
      <el-button @click="patchdicontract" type="primary" v-if="showMultiDepartmentButton" >多部门分配表</el-button>
      <el-button @click="patchtuisong" type="primary" >推送OA审批</el-button>
      <el-button @click="patchoarouter" type="primary" >跳转OA界面</el-button>
      <el-button @click="patchoadelete" type="primary" >废弃OA审批</el-button>
      <!--    <el-button @click="ceshi" type="primary" >测试查询</el-button>-->
      <OAendorse ref="OAendorseRef" :executingDeparment="patch.executingDeparment" :contractId="one.patchId" :handle="patch.handler" :projectId="patch.projectId"/>
    </ContentWrap>
  </ContentWrap>

    <!-- 在现有模板中添加弹窗 -->
  <el-dialog v-model="allocationDialogVisible" title="部门分配设置" width="60%">
    <el-form ref="allocationFormRef" :model="allocationFormData" label-width="120px">
      <el-row v-for="(item, index) in allocationFormData.departments" :key="index" :gutter="20" class="mb-3">
        <!-- 部门名称 -->
        <el-col :span="7">
          <el-form-item label="部门名称">
            <el-input v-model="item.departmentName" disabled />
          </el-form-item>
        </el-col>

        <!-- 分配比例 -->
        <el-col :span="6">
          <el-form-item label="分配比例(%)">
            <el-input
              v-model="item.proportion"
              :min="0"
              :max="100"
              @change="handleProportionChange(item)"
              class="w-full"
            />
          </el-form-item>
        </el-col>

        <!-- 分配金额 -->
        <el-col :span="6">
          <el-form-item label="分配金额">
            <el-input
              v-model="item.amount"
              :min="0"
              @change="handleAmountChange(item)"
              class="w-full"
            />
          </el-form-item>
        </el-col>

        <!-- 删除按钮 -->
<!--        <el-col :span="5">-->
<!--          <el-button @click="removeDepartment(index)" type="danger" plain>删除</el-button>-->
<!--        </el-col>-->
      </el-row>

      <!-- 添加部门按钮 -->
<!--      <el-row class="mt-4">-->
<!--        <el-col :span="24">-->
<!--          <el-button @click="addDepartment" type="primary">添加部门</el-button>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <!-- 总比例和总金额 -->
      <el-row class="mt-4">
        <el-col :span="12">
          <el-form-item label="总比例">
            <div class="font-bold">{{ totalProportiondi }}%</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总金额">
            <div class="font-bold">￥{{ totalAmount }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="allocationDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitAllocation">保存</el-button>
    </template>
  </el-dialog>


</template>

<script setup lang="ts">
import {ContractApi, ContractVO} from '@/api/projectmanage/contract'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict"
import { DicontractApi } from '@/api/projectmanage/dicontract'
import {reactive, ref, onMounted, defineOptions} from "vue"
import {ElSelect, ElOption} from 'element-plus'
import {CustomerApi} from "@/api/projectmanage/customer"
import {ElMessage, ElMessageBox} from 'element-plus'
import {FeeApi} from "@/api/projectmanage/fee";
import {ProjectApi} from "@/api/projectmanage/project";
import {useUserStore} from '@/store/modules/user';
import {accAdd} from "@/utils/add_sub";
import UploadFile from "@/components/UploadFile/src/UploadFile.vue";
import {BusinessFileTypeApi} from "@/api/infraInfo/bussinessFileType";
import { ElDialog } from 'element-plus';
import ContractPatch from '@/views/projectmanage/contract/detail/ContractPatch.vue';
import { watch, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import OAendorse from "@/views/projectmanage/contract/OAendorse.vue";
import { pinyin } from 'pinyin'
import {ProjectManagerApprovalApi} from "@/api/projectmanage/projectmanagerapproval";
import { debounce } from 'lodash-es'








/** 合同 表单 */
const route = useRoute();
const isFormDirty = ref(false);
const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const groupFileList = ref([])
const patchgroupFileList = ref([])
const patchfileList = ref([])
const fileList = ref([])
const feeList = ref([])
const patchfeeList = ref([])
const one = ref({
  id: undefined,
  contractType: undefined,
  handler: undefined,
  executingDeparment: undefined,
  businessType: undefined,
  startTime: undefined,
  endTime: undefined,
  advanceAmount: undefined,
  status: undefined,
  approvalStatus: undefined,
  projectId: undefined,
  projectName: undefined,
  projectGroupId: undefined,
  projectGroupCode: undefined,
  projectGroupName: undefined,
  projecttype: undefined,
  contractCode: undefined,
  contractName: undefined,
  jiafangId: undefined,
  jiafangName: undefined,
  workDay: undefined,
  yifangId: undefined,
  yifangName: undefined,
  jiafangSignatory: undefined,
  yifangSignatory: undefined,
  signingTime: undefined,
  content: undefined,
  amount: undefined,
  currency: undefined,
  paymentRelationship: undefined,
  contractYear: undefined,
  isAdvancePayment: undefined,
  advanceCurrency: undefined,
  projectCode: undefined,
  remark: undefined,
  financialClassification: undefined,
  businessContractCode: undefined,
  sourceMethods: undefined,
  warrantyService: undefined,
  isWarranty: undefined,
  warrantyAmount: undefined,
  rate: undefined,
  ratesAndAmounts: [] as { rate: number | undefined, amount: number | undefined }[],
  paymentType: undefined,
  paymentDescription: undefined,
  taxRate: undefined,
  taxAmount: undefined,
  taxExclusiveAmount: undefined,
  mainOrPatch:undefined,
  mainId:undefined,
  patchId:undefined,
  warrantyDueTime: undefined,
  ifWorkday: undefined,
  ifFuwuService: undefined,
  ifWarrantyTime: undefined,
  otherName: undefined,
})

/** 补充合同信息 */
const patch=ref({
  id: undefined,
  contractType: undefined,
  handler: undefined,
  executingDeparment: undefined,
  businessType: undefined,
  startTime: undefined,
  endTime: undefined,
  advanceAmount: undefined,
  status: undefined,
  approvalStatus: undefined,
  projectId: undefined,
  projectName: undefined,
  projectGroupId: undefined,
  projectGroupCode: undefined,
  projectGroupName: undefined,
  projecttype: undefined,
  contractCode: undefined,
  contractName: undefined,
  jiafangId: undefined,
  jiafangName: undefined,
  workDay: undefined,
  yifangId: undefined,
  yifangName: undefined,
  jiafangSignatory: undefined,
  yifangSignatory: undefined,
  signingTime: undefined,
  content: undefined,
  amount: undefined,
  currency: undefined,
  paymentRelationship: undefined,
  contractYear: undefined,
  isAdvancePayment: undefined,
  advanceCurrency: undefined,
  projectCode: undefined,
  remark: undefined,
  financialClassification: undefined,
  businessContractCode: undefined,
  sourceMethods: undefined,
  warrantyService: undefined,
  isWarranty: undefined,
  warrantyAmount: undefined,
  rate: undefined,
  ratesAndAmounts: [] as { rate: number | undefined, amount: number | undefined }[],
  paymentType: undefined,
  paymentDescription: undefined,
  taxRate: undefined,
  taxAmount: undefined,
  taxExclusiveAmount: undefined,
  mainOrPatch:undefined,
  mainId:undefined,
  patchId:undefined,
  warrantyDueTime: undefined,
  ifWorkday: undefined,
  ifFuwuService: undefined,
  ifWarrantyTime: undefined,
  otherName: undefined,
})
const userStore = useUserStore()
isFormDirty.value = false
const formRef = ref() // 表单 Ref
const nameOptions = ref([])
const list = ref([])
// onMounted(async () => {
//   try {
//     nameOptions.value = await CustomerApi.getCustomerName();
//     payments.value = await FeeApi.getFeeByContractId(route.params.contractId);
//     list.value = payments.value.map(payment => ({
//       paymentType: payment.type,
//       taxAmount: payment.price,
//       taxExclusiveAmount: payment.noTaxPrice,
//       taxRate: payment.taxRate,
//       paymentDescription: payment.remarks
//     }));
//   } catch (error) {
//     console.error(
//       '获取失败', error
//     )
//   }
// })
/** 监听有无质保金 */
watch(feeList, (newFeeList) => {
  checkForWarranty(newFeeList, one);
}, { deep: true });

// 监听 patchfeeList 的变化
watch(patchfeeList, (newPatchFeeList) => {
  checkForWarranty(newPatchFeeList, patch);
}, { deep: true });

// 检查款项类型并更新"有无质保金"字段
const checkForWarranty = (feeList, target) => {
  const hasWarranty = feeList.some(fee => fee.type === '质保金');
  console.log('显示有质保金' + hasWarranty )
  if (hasWarranty) {
    if((!one.value.isWarranty) || one.value.isWarranty ==='否'){
      message.error('检测到结算信息中有质保金款项类型，请设置质保金信息');
    }
  }
};

// 监听表单数据的变化
watch(one, (newVal, oldVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    isFormDirty.value = true;
    localStorage.setItem('isFormDirty', 'true');
    localStorage.setItem('formData', JSON.stringify(one.value));
  }
}, { deep: true });

onMounted(() => {
  // 恢复临时存储的数据
  const savedFormData = localStorage.getItem('formData');
  if (savedFormData) {
    one.value = JSON.parse(savedFormData);
    localStorage.removeItem('formData'); // 清除临时存储的数据
  }
});
const beforeUnmount = () => {
  if (isFormDirty.value) {
    localStorage.setItem('isFormDirty', 'false');
  }
};

onBeforeUnmount(beforeUnmount);
/** 金额规则 */
const handleAmountInput = (value: string) => {
  // 移除非数字和小数点的字符
  let newValue = value.replace(/[^\d.]/g, '')

  // 确保只有一个小数点
  const dotIndex = newValue.indexOf('.')
  if (dotIndex !== -1) {
    // 保留第一个小数点，移除其他小数点
    newValue = newValue.slice(0, dotIndex + 1) +
      newValue.slice(dotIndex + 1).replace(/\./g, '')

    // 限制小数点后最多两位
    const parts = newValue.split('.')
    if (parts[1]?.length > 2) {
      newValue = parts[0] + '.' + parts[1].slice(0, 2)
    }
  }
}


const tableData: User[] = [
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
]

const getTaxAndNoTaxPrice = (row) => {
  console.log(row)
  if (row.amount && row.taxRate) {
    row.tax = (row.amount * row.taxRate / 100).toFixed(2)
    row.noTaxPrice = (row.amount - row.tax).toFixed(2)
  }
  if (row.amount) {
    row.proportion = ((parseFloat(row.amount) / parseFloat(one.value.amount)) * 100).toFixed(2)
  }
}

/** 刷新页面重新进入 */
const handleDialogClose = (newValue: boolean) => {
  dialogVisible.value = newValue;
  if (!newValue) {
    location.reload();
  }
};

function createWatcher(propertyPrefix) {
  return (newValue, oldValue) => {
    if (newValue) {
      const selectedOption = nameOptions.value.find(option => option.name === newValue)
      if (selectedOption) {
        one.value[`${propertyPrefix}Id`] = selectedOption.id
      } else {
        one.value[`${propertyPrefix}Id`] = ''
      }
    } else {
      one.value[`${propertyPrefix}Id`] = ''
    }
  }
}

/** 补充合同按钮 */
const dialogVisible = ref(false);
const patchForm = () => {
  dialogVisible.value = true;
}
const patchgetNoTaxPrice = computed(() => {
  if (!patch.value.ratesAndAmounts || patch.value.ratesAndAmounts.length === 0) {
    return '0.00';
  }
  let sum = 0;
  for (let i = 0; i < patch.value.ratesAndAmounts.length; i++) {
    const item = patch.value.ratesAndAmounts[i]
    sum += parseFloat((item.amount / (1 + item.rate / 100)).toFixed(2))
  }
  return sum.toFixed(2);
})

const patchgetTaxPrice = computed(() => {
  if (!patch.value.ratesAndAmounts || patch.value.ratesAndAmounts.length === 0) {
    return '0.00';
  }
  let sum = 0;
  for (let i = 0; i < patch.value.ratesAndAmounts.length; i++) {
    const item = patch.value.ratesAndAmounts[i]
    sum += parseFloat(((item.amount / (1 + item.rate / 100) * (item.rate / 100)).toFixed(2)))
  }
  return sum.toFixed(2);
})

const patchhandleAddRateAndAmount = () => {
  // 判断 patchratesAndAmounts中的所有税率和含税金额是否都有值
  for (let i = 0; i < patch.value.ratesAndAmounts.length; i++) {
    console.log("1")
    console.log(patch.value.ratesAndAmounts[i].rate)
    console.log(patch.value.ratesAndAmounts[i].amount)
    console.log(patch.value.ratesAndAmounts[i].rate || patch.value.ratesAndAmounts[i].amount)
    if (!patch.value.ratesAndAmounts[i].rate || !patch.value.ratesAndAmounts[i].amount) {
      message.error('请先填写完整税率和含税金额,再新增');
      return;
    }
  }

  console.log('Before adding:', patch.value.ratesAndAmounts);
  if (!patch.value.ratesAndAmounts) {
    patch.value.ratesAndAmounts = [];
  }
  patch.value.ratesAndAmounts.push({rate: undefined, amount: undefined});
};
const patchhandleRemoveRateAndAmount = (index: number) => {
  patch.value.ratesAndAmounts.splice(index, 1);
}
watch(() => patch.value.ratesAndAmounts, (newRatesAndAmounts) => {
  if (!newRatesAndAmounts) {
    return;
  }
  const totalAmount = newRatesAndAmounts.reduce((sum, item) => {
    const amount = item.amount !== null ? parseFloat(item.amount || '0') : 0;
    return accAdd(sum, amount);
  }, 0);
  patch.value.amount = (totalAmount).toFixed(2).toString();
  patch.value.rate = newRatesAndAmounts
    .filter(item => item.rate !== null)
    .map(item => item.rate);
}, {deep: true});
const patchfeeListtotalAmount = computed(() => {
  return patchfeeList.value.reduce((sum, item) => {
    // return   sum + (Number(item.amount) || 0)
    return accAdd(sum, (Number(item.amount) || 0))
  }, 0)
})
const patchproportionBlur = (row) => {
  if (row.proportion) {
    row.amount = (parseFloat(patch.value.amount) * row.proportion / 100).toFixed(2)
  }

}
const patchgetTaxAndNoTaxPrice = (row) => {
  console.log(row)
  if (row.amount && row.taxRate) {
    row.tax = (row.amount * row.taxRate / 100).toFixed(2)
    row.noTaxPrice = (row.amount - row.tax).toFixed(2)
  }
  if (row.amount) {
    row.proportion = ((parseFloat(row.amount) / parseFloat(patch.value.amount)) * 100).toFixed(2)
  }
}
const patchhandleRemoveFee = async (index: number) => {
  const removedFee = patchfeeList.value[index]; // 存储被删除的行的信息
  patchfeeList.value.splice(index, 1);
  console.log(index);
  console.log(removedFee.id); // 打印被删除的行的信息
  const id = removedFee.id
  await FeeApi.deleteFee(id)
  message.success(t('删除成功'))

}
const addpatchRow = () => {
  // list.value.push({
  //   paymentType: '',
  //   taxAmount: '',
  //   taxExclusiveAmount: '',
  //   taxRate: '',
  //   paymentDescription: ''
  // });

  // 前面的以及填完了才让新增
  for (let i = 0; i < patchfeeList.value.length; i++) {
    if (!patchfeeList.value[i].type || !patchfeeList.value[i].amount || !patchfeeList.value[i].proportion) {
      message.error('请先填写完整款项信息,再新增');
      return;
    }
  }

  // 如果patchfeeList的amount和,已经等于或者大于one.value.amount,则不再允许新增
  const totalAmount = patchfeeList.value.reduce((sum, item) => {
    const amount = item.amount ? parseFloat(item.amount) : 0;
    return accAdd(sum, amount);
  }, 0)
  if (patch.value.financialClassification !== '服务') {
    if (totalAmount >= parseFloat(patch.value.amount || '0')) {
      message.error('款项总金额已经等于或者大于合同金额，不允许新增');
      return;
    }
  }

  patchfeeList.value.push({
    type: '',
    amount: '',
    taxRate: '',
    noTaxPrice: '',
    tax: '',
    remarks: ''
  });
}

const proportionBlur = (row) => {
  if (row.proportion) {
    row.amount = (parseFloat(one.value.amount) * row.proportion / 100).toFixed(2)
  }
}
const getNoTaxPrice = computed(() => {
  if (!one.value.ratesAndAmounts || one.value.ratesAndAmounts.length === 0) {
    return '0.00';
  }

  let sum = 0;
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    const item = one.value.ratesAndAmounts[i];
    if (item.amount !== undefined && item.rate !== undefined) {
      sum += parseFloat((item.amount / (1 + item.rate / 100)).toFixed(2));
    }
  }
  return sum.toFixed(2);
})

const getTaxPrice = computed(() => {
  if (!one.value.ratesAndAmounts || one.value.ratesAndAmounts.length === 0) {
    return '0.00';
  }
  let sum = 0;
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    const item = one.value.ratesAndAmounts[i]
    sum += parseFloat(((item.amount / (1 + item.rate / 100) * (item.rate / 100)).toFixed(2)))
  }
  return sum.toFixed(2);
})

const router = useRouter();
watch(() => one.value.jiafangName, createWatcher('jiafang'))
watch(() => one.value.yifangName, createWatcher('yifang'))

const oneRules = reactive({
  contractName: [{required: true, message: '合同名称不能为空', trigger: 'blur'}],
  paymentRelationship: [{required: true, message: '收付关系不能为空', trigger: 'blur'}],
  contractCode: [{required: true, message: '合同编码不能为空', trigger: 'blur'}],
})

const addRow = () => {
  // list.value.push({
  //   paymentType: '',
  //   taxAmount: '',
  //   taxExclusiveAmount: '',
  //   taxRate: '',
  //   paymentDescription: ''
  // });

  // 前面的以及填完了才让新增
  for (let i = 0; i < feeList.value.length; i++) {
    if (!feeList.value[i].type || !feeList.value[i].amount || !feeList.value[i].proportion) {
      message.error('请先填写完整款项信息,再新增');
      return;
    }
  }

  // 如果feeList的amount和,已经等于或者大于one.value.amount,则不再允许新增
  const totalAmount = feeList.value.reduce((sum, item) => {
    const amount = item.amount ? parseFloat(item.amount) : 0;
    return accAdd(sum, amount);
  }, 0)
  if (one.value.financialClassification !== '服务') {
    console.log(one.value.financialClassification)
    if (totalAmount >= parseFloat(one.value.amount || '0')) {
      // console.log('2000',parseFloat(one.value.amount || '0'))
      // console.log('11111',totalAmount)
      message.error('款项总金额已经等于或者大于合同金额，不允许新增');
      return;
    }
  }

  feeList.value.push({
    type: '',
    amount: '',
    taxRate: '',
    noTaxPrice: '',
    tax: '',
    remarks: ''
  });
}


const collectedUrls = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  return group.map(item => item.url);
};

const collectUrl2NameMap = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  const url2NameMap = {};
  group.forEach(item => {
    url2NameMap[item.url] = item.name;
  });
  return url2NameMap;
};


const tableRowClassName = ({row, rowIndex,}) => {
  if (one.value.paymentRelationship == '收') {
    return 'red-row'
  } else {
    return 'green-row'
  }
}


const handleAddRateAndAmount = () => {
  // 判断 ratesAndAmounts中的所有税率和含税金额是否都有值
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    console.log("1")
    console.log(one.value.ratesAndAmounts[i].rate)
    console.log(one.value.ratesAndAmounts[i].amount)
    console.log(one.value.ratesAndAmounts[i].rate || one.value.ratesAndAmounts[i].amount)
    if (!one.value.ratesAndAmounts[i].rate || !one.value.ratesAndAmounts[i].amount) {
      message.error('请先填写完整税率和含税金额,再新增');
      return;
    }
  }

  console.log('Before adding:', one.value.ratesAndAmounts);
  if (!one.value.ratesAndAmounts) {
    one.value.ratesAndAmounts = [];
  }
  one.value.ratesAndAmounts.push({rate: undefined, amount: undefined});
};

const handleRemoveRateAndAmount = (index: number) => {
  one.value.ratesAndAmounts.splice(index, 1);
}

watch(() => one.value.ratesAndAmounts, (newRatesAndAmounts) => {
  if (!newRatesAndAmounts) {
    return;
  }
  const totalAmount = newRatesAndAmounts.reduce((sum, item) => {
    const amount = item.amount !== null ? parseFloat(item.amount || '0') : 0;
    return sum + amount;
  }, 0);
  one.value.amount = (totalAmount).toFixed(2).toString();
  one.value.rate = newRatesAndAmounts
    .filter(item => item.rate !== null)
    .map(item => item.rate);
}, {deep: true});

const workDayInput = async (value: number) => {
  // 移除非数字的字符
  // String date, Integer days
  if (one.value.signingTime && value) {
    const data = await ContractApi.addDays({
      date: one.value.signingTime,
      days: value
    })
    one.value.endTime = data
  }
}

const signingTimeChange = async (value: string) => {
  // 移除非数字的字符
  console.log(value)
  // String date, Integer days
  if (one.value.workDay && value) {
    const data = await ContractApi.addDays({
      date: value,
      days: one.value.workDay
    })
    one.value.endTime = data
  }
}

const money_format = (number, decimals, dec_point, thousands_sep) => {
  /*
    * 参数说明：
    * number：要格式化的数字
    * decimals：保留几位小数
    * dec_point：小数点符号
    * thousands_sep：千分位符号
    * */
  number = (number + '').replace(/[^0-9+-Ee.]/g, '')
  let n = !isFinite(+number) ? 0 : +number,

    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
    sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
    dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
    s = '',
    toFixedFix = function (n, prec) {
      var k = Math.pow(10, prec)
      return '' + Math.floor(n * k) / k
    }
  s = (prec ? toFixedFix(n, prec) : '' + Math.floor(n)).split('.')
  var re = /(-?\d+)(\d{3})/
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, '$1' + sep + '$2')
  }

  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
  // 使用案例
  //   number_format(1234567.089, 2, ".", ",");//1,234,567.08
}


onMounted(async () => {
  loading.value = true
  try {
    await getCustomerOptions()
    nameOptions.value = await CustomerApi.getCustomerName();

    try {
      if (route.params.contractId === '0') {
        // 初始化一个空的合同对象
        one.value = {
          id: undefined,
          contractType: undefined,
          handler: undefined,
          executingDeparment: undefined,
          businessType: undefined,
          startTime: undefined,
          endTime: undefined,
          advanceAmount: undefined,
          status: undefined,
          approvalStatus: undefined,
          projectId: undefined,
          projectName: undefined,
          projectGroupId: undefined,
          projectGroupCode: undefined,
          projectGroupName: undefined,
          contractCode: undefined,
          contractName: undefined,
          jiafangId: undefined,
          jiafangName: undefined,
          workDay: undefined,
          yifangId: undefined,
          yifangName: undefined,
          jiafangSignatory: undefined,
          yifangSignatory: undefined,
          signingTime: undefined,
          content: undefined,
          amount: undefined,
          currency: undefined,
          paymentRelationship: undefined,
          contractYear: undefined,
          isAdvancePayment: undefined,
          advanceCurrency: undefined,
          projectCode: undefined,
          remark: undefined,
          financialClassification: undefined,
          businessContractCode: undefined,
          sourceMethods: undefined,
          warrantyService: undefined,
          isWarranty: undefined,
          warrantyAmount: undefined,
          rate: undefined,
          ratesAndAmounts: [] as { rate: number | undefined, amount: number | undefined }[],
          paymentType: undefined,
          paymentDescription: undefined,
          taxRate: undefined,
          taxAmount: undefined,
          taxExclusiveAmount: undefined,
          mainOrPatch:undefined,
          mainId:undefined,
          patchId:undefined,
          warrantyDueTime: undefined,
          ifWorkday: undefined,
          ifFuwuService: undefined,
          ifWarrantyTime: undefined,
        }
        //合同经办人默认当前用户
        one.value.handler = userStore.user.nickname
        // 状态默认为未开始
        one.value.status = '未开始'
        // 币种默认人名币
        one.value.currency = '人民币'
        // 如果是新增，查询项目，根据项目id
        // 年份默认为当前年
        one.value.contractYear = new Date().getFullYear().toString();
        // 合同编号
        // const data = await ContractApi.getContractCode({
        //   year: one.value.contractYear
        // });
        // one.value.contractCode = data
        // 审核状态为 未审批
        one.value.approvalStatus = '未审批'
        if (route.params.projectId) {
          const project = await ProjectApi.getProject(route.params.projectId);
          one.value.projectId = project.id;
          one.value.projectName = project.projectName;
          one.value.projectCode = project.projectCode;
          one.value.contractCode = project.projectCode.replace(/^G/, 'C');
          one.value.projectGroupId = project.projectGroupId;
          one.value.projectGroupCode = project.projectGroupCode;
          one.value.projectGroupName = project.projectGroupName;
          // 项目的收付关系赋值给合同的收付关系
          one.value.paymentRelationship = project.payReciRelation;
          // 履行部门默认为项目的管理部门
          one.value.executingDeparment = project.managingDepartment;
          // 甲方默认为项目的付款方
          one.value.jiafangId = project.payerId
          one.value.jiafangName = project.payerName
          if (project.payReciRelation === '付') {
            one.value.jiafangName = '广州港数据科技有限公司'
          } else if (project.payReciRelation === '收') {
            one.value.yifangName = '广州港数据科技有限公司'
            // 收款合同，来源方式都是直接委托
            one.value.sourceMethods = '直接委托'
          }
          // 合同名称默认为项目名称
          one.value.contractName = project.projectName;
          // 合同概要默认为项目名称
          one.value.remark = project.projectName
          one.value.projecttype = project.type
        }

      } else {
        // 获取合同详情
        const data = await ContractApi.getContract(route.params.contractId);
        one.value = reactive(data);
        if (route.params.projectId) {
          const project = await ProjectApi.getProject(route.params.projectId);
          one.value.projecttype = project.type
        }
        if(data.patchId != null)
        {
          const patchdata = await ContractApi.getContract(data.patchId);
          patch.value = reactive(patchdata)
          console.log(patch)
          patchfeeList.value= patchdata.fees || [];
          await getpatchGroupFileList()
        }

        // list.value = data.payments?.map(payment => ({
        //   paymentType: payment.type,
        //   taxAmount: payment.price,
        //   taxExclusiveAmount: payment.noTaxPrice,
        //   taxRate: payment.taxRate,
        //   paymentDescription: payment.remarks
        // })) || [];

        feeList.value = data.fees || [];
      }

      await getGroupFileList()


    } catch (error) {
      console.error('获取失败', error);
    } finally {
      loading.value = false;
    }
  } catch (error) {
    console.error('获取客户列表失败:', error)
  }
});

const getGroupFileList = async () => {

  let queryParams = {
    fileBusinessType: `合同`,
    businessId: one.value.id,
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    fileList.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < fileList.value.length; i++) {
      let item = fileList.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    groupFileList.value = Object.values(group)
  } finally {

  }
};
const getpatchGroupFileList = async () => {

  let queryParams = {
    fileBusinessType: `合同`,
    businessId: one.value.patchId,
  }
  console.log(queryParams)
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    patchfileList.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < patchfileList.value.length; i++) {
      let item = patchfileList.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    patchgroupFileList.value = Object.values(group)
  } finally {

  }
};
const returnXM = () => {
  const projectId = route.params.projectId
  if (!projectId) {
    ElMessage.error('项目ID不存在')
    return
  }

  router.push(`/project/${projectId}`)
}
const loading = ref(false)
defineOptions({
  name: 'ContractDetail'
});


const proportionChange = (row) => {
  if (row.proportion > 100) {
    row.proportion = 100
  }
}

const totalProportion = computed(() => {
  return feeList.value.reduce((sum, item) => {
    // return sum + (Number(item.proportion) || 0)
    return accAdd(sum, (Number(item.proportion) || 0))
  }, 0)
})

const feeListtotalAmount = computed(() => {
  return feeList.value.reduce((sum, item) => {
    // return   sum + (Number(item.amount) || 0)
    return accAdd(sum, (Number(item.amount) || 0))
  }, 0)
})

const handleRemoveFee = async (index: number) => {
  const removedFee = feeList.value[index]; // 存储被删除的行的信息
  feeList.value.splice(index, 1);
  console.log(index);
  console.log(removedFee.id); // 打印被删除的行的信息
  const id = removedFee.id
  await FeeApi.deleteFee(id)
  message.success(t('删除成功'))
}

const OAendorseRef = ref();
const approves = ref([])

const tuisong = async () => {
  const resList = await ContractApi.getdicontract({ contractId: route.params.contractId })
  if (route.params.contratId !== '0' && showMultiDepartmentButton.value){
    if (resList.length <= 1){
      console.log('有值吗',resList.length)
      ElMessage.error('多个部门需填写部门分配表')
      return
    }
    console.log('有值',resList.length)
  }
  console.log(OAendorseRef.value)
  if (!OAendorseRef.value ) {
    console.error('OAendorseRef is not initialized');
    return;
  }
  // 检查 合同附件 是否为空
  // if (group[0].fileBusinessTypeDetail === 0) {
  //   ElMessage.warning('请上传相关文件。');
  //   return;
  // }
  const requiredFields = [
    { field: 'jiafangName', label: '甲方名称' },
    { field: 'yifangName', label: '乙方名称' },
    { field: 'remark', label: '合同概要' },
    { field: 'executingDeparment', label: '主办部门' },
    { field: 'handler', label: '经办人'}
  ];
  for (const { field, label } of requiredFields) {
    if (!one.value[field]) {
      ElMessage.error(`请补充${label}，并进行刷新保存操作`);
      return;
    }
  }
//  进行更新操作
//   const data = one.value as unknown as ContractVO;
//   await ContractApi.updateContract({...data, projectId});
  approves.value= OAendorseRef.value.getApproverList();


  if(route.params.contractId === '0'){
    ElMessage.error(t('未找到合同id，请重试！！'));
  }else{
    if(one.value.status == '未开始'){
      await ContractApi.push({
        contractId: contractId,
        approvers: approves.value
      })
      isFormDirty.value = false;   //取消守卫路由
      const data = one.value as unknown as ContractVO;
      data.status = 'OA审批中'
      await ContractApi.updateContract({...data, projectId});
      ElMessage.success(t('推送成功，请前往OA审批！！'));
    }else{
      ElMessage.error(t('合同已推送OA或已废弃，请重试！！'));
    }
  }
}
const oarouter = async () => {
  const timeTick = Date.now().toString();//时间戳
  const name = await ProjectManagerApprovalApi.getName()
  // const name = one.value.handler
  const yonghu = pinyin(name, {style: pinyin.STYLE_NORMAL}).flat().join('');
  const data=await ContractApi.oarouter({
    yonghu:yonghu,
    timeTick:timeTick
  })
  const signature = data          //获取的临时口令
  const flowId = await ContractApi.getflowId({
    contractId: contractId
  })
  if(flowId!=null){
    let encodedFlowId = encodeURIComponent(flowId);    //flowId转URL码
    let nexturl = one.value.status === '已归档'
      ? `/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${encodedFlowId}`
      : `/km/review/km_review_main/kmReviewMain.do?method=edit&fdId=${encodedFlowId}`
    const url=`https://oa.gzport.com/oa/api/auth/login_by_sso?sso=customer_sso&loginName=${yonghu}&tickTime=${timeTick}&signature=${signature}&next=`+escape(nexturl)
    console.log(url)
    window.open(url);
  }else {
    ElMessage.error(t('未找到合同OA，请重试！！'));
  }
}

const projectId = route.params.projectId;
const contractId = route.params.contractId;

// const ceshi = async () => {
//   await ContractApi.getResult()
//
// }
const oadelete = async () => {
  const flowId = await ContractApi.getflowId({
    contractId: contractId
  })
  if(flowId!=null){
    console.log(flowId)
    await ContractApi.abandon({ flowId });
    const data = one.value as unknown as ContractVO;
    data.status = 'OA废弃'
    await ContractApi.updateContract({...data, projectId});
    isFormDirty.value = false
    ElMessage.success(t('删除成功，请前往OA审批！！'));
  }else{
    ElMessage.error(t('未找到合同OA，请重试！！'));
  }
}
const patchsubmitForm = async () => {
  try {
    const patchdata = patch.value as unknown as ContractVO;
    console.log('kankanakankan',patchdata)
    await ContractApi.updateContract({...patchdata, projectId});
    console.log(patchdata)
    isFormDirty.value = false
    localStorage.setItem('isFormDirty', 'false');
    ElMessage.success(t('补充合同信息修改成功'));
  }catch (error) {
    console.error('补合同更新失败', error);
    ElMessage.error(t('common.updateFail'));
  }
}
const patchtuisong = async () => {
  const resList = await ContractApi.getdicontract({ contractId: patch.value.id })
  if (route.params.contratId !== '0' && showMultiDepartmentButton.value){
    if (resList.length <= 1){
      console.log('有值吗',resList.length)
      ElMessage.error('多个部门需填写部门分配表')
      return
    }
    console.log('有值',resList.length)
  }
  if (!OAendorseRef.value) {
    console.error('OAendorseRef is not initialized');
    return;
  }
  // 检查 合同附件 是否为空
  // if (group[0].fileBusinessTypeDetail === 0) {
  //   ElMessage.warning('请上传相关文件。');
  //   return;
  // }
  const requiredFields = [
    { field: 'jiafangName', label: '甲方名称' },
    { field: 'yifangName', label: '乙方名称' },
    { field: 'remark', label: '合同概要' },
    { field: 'executingDeparment', label: '主办部门' },
    { field: 'handler', label: '经办人'}
  ];
  for (const { field, label } of requiredFields) {
    if (!patch.value[field]) {
      ElMessage.error(`请补充${label}，并进行刷新保存操作`);
      return;
    }
  }
//  进行更新操作
//   const data = one.value as unknown as ContractVO;
//   await ContractApi.updateContract({...data, projectId});
  approves.value= OAendorseRef.value.getApproverList();


  if(route.params.contractId === '0'){
    ElMessage.error(t('未找到合同id，请重试！！'));
  }else{
    if(patch.value.status == '未开始'){
      await ContractApi.push({
        contractId: one.value.patchId,
        approvers: approves.value
      })
      isFormDirty.value = false;   //取消守卫路由
      const data = patch.value as unknown as ContractVO;
      data.status = 'OA审批中'
      await ContractApi.updateContract({...data, projectId});
      ElMessage.success(t('推送成功，请前往OA审批！！'));
    }else{
      ElMessage.error(t('合同已推送OA或已废弃，请重试！！'));
    }
  }
}
const patchoarouter = async () => {
  const timeTick = Date.now().toString();//时间戳
  const name = await ProjectManagerApprovalApi.getName()
  // const name = one.value.handler
  const yonghu = pinyin(name, {style: pinyin.STYLE_NORMAL}).flat().join('');
  const data=await ContractApi.oarouter({
    yonghu:yonghu,
    timeTick:timeTick
  })
  const signature = data          //获取的临时口令
  const flowId = await ContractApi.getflowId({
    contractId: one.value.patchId
  })
  if(flowId!=null){
    let encodedFlowId = encodeURIComponent(flowId);    //flowId转URL码
    let nexturl = `/km/review/km_review_main/kmReviewMain.do?method=edit&fdId=${encodedFlowId}`
    const url=`https://oa.gzport.com/oa/api/auth/login_by_sso?sso=customer_sso&loginName=${yonghu}&tickTime=${timeTick}&signature=${signature}&next=`+escape(nexturl)
    console.log(url)
    window.open(url);
  }else {
    ElMessage.error(t('未找到合同OA，请重试！！'));
  }
}


const sanheyi = async () => {
  console.log('123456',OAendorseRef.value.getApproverList())

};

const patchoadelete = async () => {
  const flowId = await ContractApi.getflowId({
    contractId: one.value.patchId
  })
  if(flowId!=null){
    console.log(flowId)
    await ContractApi.abandon({ flowId });
    const data = one.value as unknown as ContractVO;
    data.status = 'OA废弃'
    await ContractApi.updateContract({...data, projectId});
    isFormDirty.value = false
    ElMessage.success(t('删除成功，请前往OA审批！！'));
  }else{
    ElMessage.error(t('未找到合同OA，请重试！！'));
  }
}

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate();
  if (!cooperationUnit.value) {
    ElMessage.error('合作单位必须填写');
    return;
  }
  // const datatofee = {
  //   payments: list.value.map(payment => ({
  //     type: payment.paymentType,
  //     price: payment.taxAmount,
  //     noTaxPrice: payment.taxExclusiveAmount,
  //     taxRate: payment.taxRate,
  //     remarks: payment.paymentDescription,
  //     contractId: contractId
  //   }))
  // }
  if (route.params.contractId === '0') {
    // 查询数据库中是否存在相同名称和金额的合同
    try {
      const checknameandamount = await ContractApi.getContractNameAndAmount(one.value.contractName, one.value.amount)
      if (checknameandamount) {
        // 存在重复项，提醒用户并进行确认
        const confirmResult = await ElMessageBox.confirm(
          '已存在相同名称和金额的合同，是否继续提交？',
          '提示',
          {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        // 用户确认后继续执行提交逻辑
        if (confirmResult !== 'confirm') {
          return;
        }
      }
    } catch (error) {
      console.error('查询合同失败', error);
      ElMessage.error('查询合同失败，请稍后再试。');
      return;
    }
  }
  // 根据 contractId 判断是新增还是更新
  if (route.params.contractId === '0') {
    // 计算feeList的和，是否等于合同金额
    const totalAmount = feeList.value.reduce((sum, item) => {
      const amount = item.amount !== null ? parseFloat(item.amount || '0') : 0;
      return sum + amount;
    }, 0);
    if (one.value.financialClassification !== '服务'){
      let contractAmount = parseFloat(one.value.amount);
      if (Math.abs(totalAmount - contractAmount) > 0.1) {
        console.log('结算金额',totalAmount)
        console.log('合同金额',one.value.amount)
        ElMessage.error('款项金额总和不等于合同金额');
        return;
      }
    }

    // 检查feeList的含税金额，税率，不含税金额，税额 是否全部都填写
    for (let i = 0; i < feeList.value.length; i++) {
      const item = feeList.value[i];
      // if (!item.type || !item.amount || !item.taxRate || !item.noTaxPrice || !item.tax) {
      if (!item.type || !item.amount) {
        ElMessage.error('请填写完整款项信息');
        return;
      }
    }
    // 检查feeList的税率是否是在合同税率集合里面
    // for (let i = 0; i < feeList.value.length; i++) {
    //   const item = feeList.value[i];
    //   if (!one.value.ratesAndAmounts.some(rateAndAmount => rateAndAmount.rate === item.taxRate)) {
    //     ElMessage.error('税率不在合同税率集合里面');
    //     return;
    //   }
    // }
    // 新增操作
    try {
      let data = one.value as unknown as ContractVO;
      let data2 = await ProjectApi.getProject(data.projectId);
      data.contractType = data2.type
      data.fees = feeList.value;
      const createdContract = await ContractApi.createContract({...data, projectId});
      // console.log(createdContract)
      // await FeeApi.createFee(datatofee);
      ElMessage.success(t('common.createSuccess'));
      console.log('新增成功' + route.params.contractId)
      // router.push({ path: '/projectmanage/contract' })
      window.location.href = `/contract/${route.params.projectId}/${createdContract}`;
      isFormDirty.value = false;
      localStorage.setItem('isFormDirty', 'false');
      // // 返回项目详情页面并传递合同信息和 projectId
      // router.push({
      //   name: 'projectData',
      //   params: {projectId},
      //   query: {contractId: createdContract.id, contract: JSON.stringify(createdContract)}
      // });

      // 跳转会项目详情页面 （不跳转了）
      // router.push({
      //   name: 'projectData',
      //   params: {projectId}
      // });


    } catch (error) {
      console.error('新增失败', error);
      ElMessage.error(t('common.createFail'));
    }
  } else {
    // 更新操作
    try {
      const data = one.value as unknown as ContractVO;
      let data2 = await ProjectApi.getProject(data.projectId);
      data.contractType = data2.type
      data.fees = feeList.value;
      await ContractApi.updateContract({...data, projectId});
      // if(data.patchId != null){
      //   const patchdata = patch.value as unknown as ContractVO;
      //   await ContractApi.updateContract({...patchdata, projectId});
      // }
      // await FeeApi.updateFee(datatofee);
      const updatedData = await ContractApi.getContract(route.params.contractId);
      feeList.value = updatedData.fees || [];

      ElMessage.success(t('common.updateSuccess'));
      console.log('修改成功' + route.params.contractId)
      isFormDirty.value = false;
      localStorage.setItem('isFormDirty', 'false');

      // 不跳转了
      // 返回项目详情页面并传递合同信息和 projectId
      // router.push({
      //   name: 'projectData',
      //   params: {projectId},
      //   // query: {contractId: route.params.contractId, contract: JSON.stringify(one.value)}   //传递id
      // });
    } catch (error) {
      console.error('更新失败', error);
      ElMessage.error(t('common.updateFail'));
    }
  }
};

// 计算合作单位
const cooperationUnit = computed({
  get: () => {
    return one.value.paymentRelationship === '收' ? one.value.jiafangName : one.value.yifangName
  },
  set: (val) => {
    if (one.value.paymentRelationship === '收') {
      one.value.jiafangName = val
      // 找到对应的ID
      const selectedOption = nameOptions.value.find(option => option.name === val)
      if (selectedOption) {
        one.value.jiafangId = selectedOption.id
      }
    } else {
      one.value.yifangName = val
      // 找到对应的ID
      const selectedOption = nameOptions.value.find(option => option.name === val)
      if (selectedOption) {
        one.value.yifangId = selectedOption.id
      }
    }
  }
})
const patchcooperationUnit = computed({
  get: () => {
    return patch.value.paymentRelationship === '收' ? patch.value.jiafangName : patch.value.yifangName
  },
  set: (val) => {
    if (patch.value.paymentRelationship === '收') {
      patch.value.jiafangName = val
      // 找到对应的ID
      const selectedOption = nameOptions.value.find(option => option.name === val)
      if (selectedOption) {
        patch.value.jiafangId = selectedOption.id
      }
    } else {
      patch.value.yifangName = val
      // 找到对应的ID
      const selectedOption = nameOptions.value.find(option => option.name === val)
      if (selectedOption) {
        patch.value.yifangId = selectedOption.id
      }
    }
  }
})

//自动保存
const handleAutoSave = debounce(() => {
  if (route.params.contractId !== '0') {  // 仅修改时触发自动保存
    submitForm({ autoSave: true })  // 添加参数区分自动保存
    console.log('自动保存成功')
  }
}, 200) // 200毫秒防抖

// 获取客户列表
const getCustomerOptions = async () => {
  try {
    const data = await CustomerApi.getCustomerName()
    nameOptions.value = data
  } catch (error) {
    console.error('获取客户列表失败:', error)
  }
}


// 新增数据
const allocationDialogVisible = ref(false)
const allocationFormRef = ref()
const allocationFormData = ref({
  departments: [] as Array<{ departmentName: string, proportion: number, amount: number }>,
  contractId: undefined as number | undefined,
  projectId: undefined as string | undefined
})

// 计算总比例和总金额
// 计算总比例（精确到两位小数）
const totalProportiondi = computed(() => {
  const sum = allocationFormData.value.departments.reduce((total, item) => {
    return accAdd(total, item.proportion || 0)
  }, 0)
  return sum.toFixed(2)
})

// 计算总金额（精确到两位小数）
const totalAmount = computed(() => {
  const sum = allocationFormData.value.departments.reduce((total, item) => {
    return accAdd(total, item.amount || 0)
  }, 0)
  return sum.toFixed(2)
})

const showMultiDepartmentButton = ref(false)
const existingData = ref<DicontractVO[]>([])

watchEffect(() => {
  if (OAendorseRef.value) {
    showMultiDepartmentButton.value = OAendorseRef.value.hasCooperativeDepartments()

  }
})
// 测试按钮点击事件
const dicontract = async () => {
  if  (!one.value.id) {
    ElMessage.error('未找到合同，请先保存合同信息')
    return
  }
  console.log('123123')
  const resList = await ContractApi.getdicontract({
    contractId: one.value.id
  })
  existingData.value = resList || []
  // 获取审批节点信息
  const approvers = OAendorseRef.value?.getApproverList() || []

  // 过滤主办和协办部门
  const departments = approvers
    .filter(node => node.name === '主办部门' || node.name.startsWith('协办部门'))
    .map(node => {
      // 查找现有数据
      const existing = resList?.find(d => d.division === node.name)
      return {
        departmentName: node.name,
        proportion: existing ? parseFloat(existing.proportion) : 0,
        amount: existing ? parseFloat(existing.amount) : 0,
        id: existing?.id // 保存原始ID用于更新
      }
    })
  // 初始化表单数据
  allocationFormData.value = {
    departments: departments,
    contractId: one.value.id,
    projectId: one.value.projectId
  }
  console.log(allocationFormData.value)

  // 显示弹窗
  allocationDialogVisible.value = true
}
const patchdicontract = async () => {
  if  (!patch.value.id) {
    ElMessage.error('未找到合同，请先保存合同信息')
    return
  }
  console.log('123123')
  const resList = await ContractApi.getdicontract({
    contractId: patch.value.id
  })
  existingData.value = resList || []
  // 获取审批节点信息
  const approvers = OAendorseRef.value?.getApproverList() || []

  // 过滤主办和协办部门
  const departments = approvers
    .filter(node => node.name === '主办部门' || node.name.startsWith('协办部门'))
    .map(node => {
      // 查找现有数据
      const existing = resList?.find(d => d.division === node.name)
      return {
        departmentName: node.name,
        proportion: existing ? parseFloat(existing.proportion) : 0,
        amount: existing ? parseFloat(existing.amount) : 0,
        id: existing?.id // 保存原始ID用于更新
      }
    })
  // 初始化表单数据
  allocationFormData.value = {
    departments: departments,
    contractId: patch.value.id,
    projectId: patch.value.projectId
  }
  console.log(allocationFormData.value)

  // 显示弹窗
  allocationDialogVisible.value = true
}
// 比例变化处理
const handleProportionChange = (item) => {
  if(one.value.patchId){
    if(patch.value.amount){
      item.amount = (parseFloat(patch.value.amount) * item.proportion / 100)
    }
  }else{
    if (one.value.amount) {
      item.amount = (parseFloat(one.value.amount) * item.proportion / 100)
    }
  }
}

// 金额变化处理
const handleAmountChange = (item) => {
  // 限制金额范围
  if (item.amount < 0) item.amount = 0
  if(!one.value.projectId){
    if (one.value.amount && parseFloat(one.value.amount) > 0) {
      item.proportion = (item.amount / parseFloat(one.value.amount)) * 100
      item.proportion = parseFloat(item.proportion.toFixed(2)) // 保留两位小数
    }
  }else {
    if (patch.value.amount && parseFloat(patch.value.amount) > 0) {
      item.proportion = (item.amount / parseFloat(patch.value.amount)) * 100
      item.proportion = parseFloat(item.proportion.toFixed(2)) // 保留两位小数
    }
  }
}

// 添加部门
const addDepartment = () => {
  ElMessageBox.prompt('请输入部门名称', '添加部门', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    if (value) {
      allocationFormData.value.departments.push({
        departmentName: value,
        proportion: 0,
        amount: 0
      })
    }
  })
}

// 移除部门
const removeDepartment = (index: number) => {
  allocationFormData.value.departments.splice(index, 1)
}

// 提交分配数据
const submitAllocation = async () => {
  // 验证数据
  if (parseFloat(totalProportiondi.value) !== 100) {
    ElMessage.error('部门分配比例总和必须为100%')
    return
  }
  if(!one.value.patchId){
    if (totalAmount.value > parseFloat(one.value.amount)) {
      ElMessage.error('分配金额总和不能超过合同金额')
      return
    }
  }else{
    if (totalAmount.value > parseFloat(patch.value.amount)) {
      ElMessage.error('分配金额总和不能超过合同金额')
      return
    }
  }

  const transformedData = allocationFormData.value.departments.map(dept => {
    // 查找原始数据（用于更新）
    const original = existingData.value.find(d => d.division === dept.departmentName)

    return {
      id: original?.id, // 如果存在原始数据则保留ID
      division: dept.departmentName,
      proportion: dept.proportion,
      amount: dept.amount,
      contractId: String(one.value.patchId ? one.value.patchId : one.value.id),
      projectId: String(one.value.projectId)
    }
  })

  try {
    // 根据是否有原始数据选择操作方式
    if (existingData.value.length > 0) {
      // 更新操作
      await Promise.all(
        transformedData.map(dept => {
          if (dept.id) { // 确保有ID才执行更新
            return DicontractApi.updateDicontract(dept)
          }
          return Promise.resolve() // 没有ID的情况（理论上不应该发生）
        })
      )
      ElMessage.success('部门分配更新成功')
    } else {
      // 新增操作
      await Promise.all(
        transformedData.map(dept =>
          DicontractApi.createDicontract(dept)
        )
      )
      ElMessage.success('部门分配保存成功')
    }

    allocationDialogVisible.value = false
  } catch (error) {
    console.error('操作失败', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 添加到 setup 中的 watch 部分
// watch(() => one.value.warrantyDueTime, (newVal) => {
//   if (newVal) {
//     const dueDate = new Date(newVal)
//     const now = new Date()
//     const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
//
//     if (diffDays <= 7 && diffDays > 0) {
//       ElMessage({
//         type: 'warning',
//         message: `提醒：质保金将在 ${diffDays} 天后到期，请及时处理！`,
//         duration: 3000,
//         showClose: true
//       })
//     } else if (diffDays <= 0) {
//       ElMessage({
//         type: 'error',
//         message: '警告：质保金已经到期，请立即处理！',
//         duration: 3000,
//         showClose: true
//       })
//     }
//   }
// }, { immediate: true })

// 关闭工期提醒
const closeWorkdayReminder = () => {
  one.value.ifWorkday = '已关闭'
  submitForm({ autoSave: true })
  console.log('自动保存成功')
}
// 关闭服务提醒
const closeFuwuServiceReminder = () => {
  one.value.ifFuwuService = '已关闭'
  submitForm({ autoSave: true })
  console.log('自动保存成功')
}
// 关闭质保金提醒
const closeWarrantyTimeReminder = () => {
  one.value.ifWarrantyTime = '已关闭'
  submitForm({ autoSave: true })
  console.log('自动保存成功')
}
</script>


<style>
.allocation-info {
  font-family: Arial, sans-serif;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.allocation-info span {
  font-weight: bold;
  color: #007bff;
  margin-right: 20px;
}

.el-table .red-row {
  background: rgba(255, 0, 0, 0.5);
}

.el-form-item__label {
  width: 103%;
  white-space: nowrap; /* 防止文本换行 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.el-table .green-row {
  background: rgba(0, 128, 0, 0.5);
}

.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
<style scoped>
.patch-contract-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  background-color: #ebf9f6;
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 4px;
}
</style>
<style scoped>
.main-contract-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  background-color: #ebf9f6;
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 4px;
}
.file-upload-section{
  margin-top: 50px;
}
</style>
