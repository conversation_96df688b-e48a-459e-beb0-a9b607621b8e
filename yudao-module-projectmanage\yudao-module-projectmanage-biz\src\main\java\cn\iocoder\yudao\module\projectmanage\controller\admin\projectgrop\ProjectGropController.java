package cn.iocoder.yudao.module.projectmanage.controller.admin.projectgrop;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.projectmanage.controller.admin.contract.vo.ContractPageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.contract.vo.ContractSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.early.vo.EarlyRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.fee.vo.FeePageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.fee.vo.FeeRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.fee.vo.FeeSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.feedetail.vo.FeeDetailPageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.feedetail.vo.FeeDetailSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.invmain.vo.InvMainPageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.invmain.vo.InvMainSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.ProjectController;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectPageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectWithFeesRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmanagerapproval.vo.ProjectManagerApprovalRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmanagerapproval.vo.ProjectManagerApprovalSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.system.vo.SystemRespVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.contract.ContractDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.early.EarlyDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.fee.FeeDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.feedetail.FeeDetailDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.invmain.InvMainDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.ProjectDO;
import cn.iocoder.yudao.module.projectmanage.dal.mysql.BusinessFileType.BusinessFileTypeMapper;
import cn.iocoder.yudao.module.projectmanage.dal.mysql.MyMapper;
import cn.iocoder.yudao.module.projectmanage.dal.mysql.projectgrop.ProjectGropMapper;
import cn.iocoder.yudao.module.projectmanage.service.contract.ContractService;
import cn.iocoder.yudao.module.projectmanage.service.early.EarlyService;
import cn.iocoder.yudao.module.projectmanage.service.fee.FeeService;
import cn.iocoder.yudao.module.projectmanage.service.feedetail.FeeDetailService;
import cn.iocoder.yudao.module.projectmanage.service.invmain.InvMainService;
import cn.iocoder.yudao.module.projectmanage.service.project.ProjectService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.math.BigDecimal;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.projectmanage.controller.admin.projectgrop.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectgrop.ProjectGropDO;
import cn.iocoder.yudao.module.projectmanage.service.projectgrop.ProjectGropService;
import cn.iocoder.yudao.module.projectmanage.controller.admin.report.vo.ReportRespVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.report.ReportDO;
import cn.iocoder.yudao.module.projectmanage.service.report.ReportService;
import cn.iocoder.yudao.module.projectmanage.controller.admin.contract.ContractController;
import cn.iocoder.yudao.module.projectmanage.controller.admin.acceptance.AcceptanceController;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmanagermaintenance.ProjectManagerMaintenanceController;
import cn.iocoder.yudao.module.projectmanage.service.plan.PlanService;
import cn.iocoder.yudao.module.projectmanage.service.weekly.WeeklyService;
import cn.iocoder.yudao.module.projectmanage.controller.admin.plan.vo.PlanRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.contract.vo.ContractRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.implement.vo.ImplementRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.launch.vo.LaunchRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.acceptance.vo.AcceptanceRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.weekly.vo.WeeklyRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmanagermaintenance.vo.ProjectManagerMaintenanceRespVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.plan.PlanDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.weekly.WeeklyDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.acceptance.AcceptanceDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.implement.ImplementDO;
import cn.iocoder.yudao.module.projectmanage.service.implement.ImplementService;
import cn.iocoder.yudao.module.projectmanage.service.launch.LaunchService;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.launch.LaunchDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectmanagermaintenance.ProjectManagerMaintenanceDO;
import cn.iocoder.yudao.module.projectmanage.service.projectmanagermaintenance.ProjectManagerMaintenanceService;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.projectperson.ProjectPersonDO;
import cn.iocoder.yudao.module.projectmanage.service.project.projectperson.ProjectPersonService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;

@Tag(name = "管理后台 - 项目组")
@RestController
@RequestMapping("/projectmanage/project-grop")
@Validated
public class ProjectGropController {

    @Resource
    private ProjectGropService projectGropService;
    @Resource
    private ProjectService projectService;
    @Autowired
    private MyMapper myMapper;
    @Resource
    private ProjectController projectController;
    @Resource
    private ContractService contractService;
    @Resource
    private ReportService reportService;
    @Resource
    private ContractController contractController;
    @Resource
    private AcceptanceController acceptanceController;
    @Resource
    private ProjectManagerMaintenanceController projectManagerMaintenanceController;
    @Resource
    private PlanService planService;
    @Resource
    private WeeklyService weeklyService;
    @Resource
    private ImplementService implementService;
    @Resource
    private LaunchService launchService;
    @Resource
    private EarlyService earlyService;
    @Resource
    private ProjectManagerMaintenanceService projectManagerMaintenanceService;
    @Resource
    private ProjectPersonService projectPersonService;
    @Resource
    private FeeService feeService;
    @Resource
    private FeeDetailService feeDetailService;
    @Resource
    private InvMainService invMainService;
    @Resource
    private AdminUserService adminUserService;
@Resource
    private ProjectGropMapper projectGropMapper;
    // 参数 groupCode,ywType
    @GetMapping("/getProjecGroupNowAmounttByGroupCodeAndYwType")
    @Operation(summary = "根据项目ID和业务类型获取项目当前金额情况")
    public CommonResult<BigDecimal> getProjecGroupNowAmounttByGroupCodeAndYwType(String groupCode, String ywType) {
        BigDecimal projectNowAmount = myMapper.selectSumDetailAmountByProjectGroupCode(groupCode, ywType);
        return success(projectNowAmount);
    }

    @PostMapping("/create")
    @Operation(summary = "创建项目组")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:create')")
    public CommonResult<Long> createProjectGrop(@Valid @RequestBody ProjectGropSaveReqVO createReqVO) {
        createReqVO.setProjectGropName(createReqVO.getProjectGropName().trim());
        return success(projectGropService.createProjectGrop(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新项目组")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:update')")
    public CommonResult<Boolean> updateProjectGrop(@Valid @RequestBody ProjectGropSaveReqVO updateReqVO) {
        // 更新projectGroup表中的记录
        projectGropService.updateProjectGrop(updateReqVO);
        // 更新project表中的记录
        ProjectPageReqVO checkProjectVO = new ProjectPageReqVO();
        checkProjectVO.setProjectGroupCode(updateReqVO.getProjectGropNumber());
        // 查出项目组名下的所有项目
        List<ProjectDO> projectList = projectService.getProjectPage(checkProjectVO).getList();
        // 对这些项目全部修改项目组名称
        for (ProjectDO projectDO : projectList) {
            // 将数据类型改成可以新增的情况
            ProjectSaveReqVO projectSaveReqVO = BeanUtils.toBean(projectDO, ProjectSaveReqVO.class);
            // 修改项目组名称
            projectSaveReqVO.setProjectGroupName(updateReqVO.getProjectGropName());
            // 更新数据到数据库
            projectService.updateProject(projectSaveReqVO);
        }
        // 返回成功结果
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除项目组")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:delete')")
    public CommonResult<Boolean> deleteProjectGrop(@RequestParam("id") Long id) {
//        projectGropService.deleteProjectGrop(id);
        // 查找项目组下是否有项目
        ProjectGropDO nowGroup = projectGropService.getProjectGrop(id);
        ProjectPageReqVO newProject = new ProjectPageReqVO();
        newProject.setProjectGroupCode(nowGroup.getProjectGropNumber());
        // 若有的话则不删除并返回错误信息
        if (projectService.getProjectPage(newProject).getTotal() > 0) {
            return error(409, "项目组名下包含项目，无法删除");
        } else {
            projectGropService.deleteProjectGrop(id);
            return success(true);
        }
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目组")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<ProjectGropRespVO> getProjectGrop(@RequestParam("id") Long id) {
        ProjectGropDO projectGrop = projectGropService.getProjectGrop(id);
        ProjectGropRespVO respVO = BeanUtils.toBean(projectGrop, ProjectGropRespVO.class);

        // 设置创建者昵称
        if (respVO.getCreator() != null && !"".equals(respVO.getCreator())) {
            AdminUserDO user = adminUserService.getUser(Long.valueOf(respVO.getCreator()));
            if (user != null) {
                respVO.setCreatorNickname(user.getNickname());
            }
        }

        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得项目组分页")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<PageResult<ProjectGropRespVO>> getProjectGropPage(@Valid ProjectGropPageReqVO pageReqVO) {
        // 获取当前登录用户的部门ID
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        // 获取项目组数据
        PageResult<ProjectGropDO> pageResult = projectGropService.getProjectGropPage(pageReqVO);
        // 将项目组数据转换为项目组响应对象
        // 将项目组数据转换为项目组响应对象，后者增加了一些字段
        PageResult<ProjectGropRespVO> bean = BeanUtils.toBean(pageResult, ProjectGropRespVO.class);

        // 获取所有项目组ID
        List<Long> projectGroupIds = bean.getList().stream()
                .map(ProjectGropRespVO::getId)
                .collect(Collectors.toList());

        // 如果项目组ID列表为空，直接返回结果
        if (projectGroupIds.isEmpty()) {
            return success(bean);
        }

        // 一次性查询所有项目组的统计数据
        List<Map<String, Object>> statsList = myMapper.selectProjectGroupStats(projectGroupIds);

        // 将统计数据转换为Map，方便查找
        Map<Long, Map<String, Object>> statsMap = statsList.stream()
                .collect(Collectors.toMap(
                        map -> ((Number) map.get("project_group_id")).longValue(),
                        map -> map
                ));

        // 获取绩效信息
        Map<Long, String> performanceInfoMap = getPerformanceInfoForProjectGroups(projectGroupIds);

        // 设置统计数据到响应对象
        for (ProjectGropRespVO projectGropRespVO : bean.getList()) {
            Map<String, Object> stats = statsMap.get(projectGropRespVO.getId());
            if (stats != null) {
                projectGropRespVO.setReceivedAmount(new BigDecimal(Optional.ofNullable(stats.get("received_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setPaidAmount(new BigDecimal(Optional.ofNullable(stats.get("paid_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setInvAmount(new BigDecimal(Optional.ofNullable(stats.get("inv_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setInvPayAmount(new BigDecimal(Optional.ofNullable(stats.get("inv_pay_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setEarliestContractYear((String) stats.get("earliest_contract_year"));
                projectGropRespVO.setWorkDay(Optional.ofNullable(stats.get("work_day"))
                        .map(val -> ((Number) val).intValue()).orElse(0));
                projectGropRespVO.setPersonCount(Optional.ofNullable(stats.get("person_count"))
                        .map(val -> ((Number) val).intValue()).orElse(0));
                projectGropRespVO.setOverFiveWanCount(Optional.ofNullable(stats.get("over_five_wan_count"))
                        .map(val -> ((Number) val).intValue()).orElse(0));
                Integer weeklyCount = Optional.ofNullable(stats.get("weekly_count"))
                        .map(val -> ((Number) val).intValue()).orElse(0);
                Integer unfinishedReceive = Optional.ofNullable(stats.get("unfinished_receive"))
                        .map(val -> ((Number) val).intValue()).orElse(0);
                Integer totalProjects = Optional.ofNullable(stats.get("total_projects"))
                        .map(val -> ((Number) val).intValue()).orElse(0);
                Integer unfinishedPay = Optional.ofNullable(stats.get("unfinished_pay"))
                        .map(val -> ((Number) val).intValue()).orElse(0);

                if (totalProjects > 0 && unfinishedReceive == 0 && unfinishedPay == 0) {
                    projectGropRespVO.setProjectGroupProgress("已完结");
                } else if (weeklyCount > 0) {
                    projectGropRespVO.setProjectGroupProgress("进行中");
                } else {
                    projectGropRespVO.setProjectGroupProgress("前期");
                }

                projectGropRespVO.setDepartment((String) stats.get("department"));

                String manager = Optional.ofNullable(projectGropRespVO.getProjectManager()).orElse("");
                String status = Optional.ofNullable((String) stats.get("manager_status"))
                        .map(s -> s.equals("审批通过") ? "已选任" : s)
                        .map(s -> "(" + s + ")")
                        .orElse("");
                projectGropRespVO.setProjectManager(manager + status);

                // 设置经办人信息
                projectGropRespVO.setHandler((String) stats.get("handler"));

                // 设置验收情况
                String reciAcceptanceStatus = (String) stats.get("reci_acceptance_status");
                String payAcceptanceStatus = (String) stats.get("pay_acceptance_status");

                // 设置收款项目验收状态
                projectGropRespVO.setReciAccepted(reciAcceptanceStatus);

                // 设置付款项目验收状态
                projectGropRespVO.setPayAccepted(payAcceptanceStatus);

                projectGropRespVO.setFlowId((String) stats.get("flow_id"));
            }
            projectGropRespVO.setStatusText(myMapper.selectStatusTextByProjectGroupId(projectGropRespVO.getId()));

            // 设置当前登录用户部门ID
            projectGropRespVO.setDeptId(deptId != null ? deptId.intValue() : null);

            // 设置绩效信息
            String performanceInfo = performanceInfoMap.get(projectGropRespVO.getId());
            projectGropRespVO.setPerformanceInfo(performanceInfo != null ? performanceInfo : "暂无绩效信息");
        }

        // 设置创建者昵称
        setCreatorNicknames(bean.getList());

        return success(bean);
    }

    @GetMapping("/count-empty-manager")
    @Operation(summary = "统计项目经理为空的项数")
    public CommonResult<Long> countEmptyProjectManager(@Valid ProjectGropPageReqVO pageReqVO) {
        // 创建分页参数获取全量数据
//        ProjectGropPageReqVO pageReqVO = new ProjectGropPageReqVO();

        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(Integer.MAX_VALUE);
//        pageReqVO.setPageSize(10000);

        //PageResult<ProjectGropDO> result = projectGropService.getProjectGropPage(pageReqVO);
        PageResult<ProjectGropDO> result;
        if (StringUtils.hasText(pageReqVO.getYear())) {
            result = projectGropService.selectProjectListByYear(pageReqVO.getYear(), pageReqVO);
        } else if (StringUtils.hasText(pageReqVO.getManagingDepartment())) {
            result = projectGropService.getProjectGropByDepartment(pageReqVO.getManagingDepartment(), pageReqVO);
        } else {
            result = projectGropService.getProjectGropPage(pageReqVO);
        }
        // 统计项目经理为空的数目
        long count = result.getList().stream()
                .filter(g -> g.getProjectManager() == null || g.getProjectManager().isEmpty())
                .count();

        return success(count);
    }

    @GetMapping("/pageWithoutManager")
    @Operation(summary = "获得项目组分页无项目经理")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<PageResult<ProjectGropRespVO>> getProjectGropPageAndManagerIsNull(@Valid ProjectGropPageReqVO pageReqVO) {
        // 获取当前登录用户的部门ID
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        // 获取项目组数据
        PageResult<ProjectGropDO> pageResult = projectGropService.getProjectGropPageAndManagerIsNull(pageReqVO);
        // 将项目组数据转换为项目组响应对象
        // 将项目组数据转换为项目组响应对象，后者增加了一些字段
        PageResult<ProjectGropRespVO> bean = BeanUtils.toBean(pageResult, ProjectGropRespVO.class);

        // 获取所有项目组ID
        List<Long> projectGroupIds = bean.getList().stream()
                .map(ProjectGropRespVO::getId)
                .collect(Collectors.toList());

        // 如果项目组ID列表为空，直接返回结果
        if (projectGroupIds.isEmpty()) {
            return success(bean);
        }

        // 一次性查询所有项目组的统计数据
        List<Map<String, Object>> statsList = myMapper.selectProjectGroupStats(projectGroupIds);

        // 将统计数据转换为Map，方便查找
        Map<Long, Map<String, Object>> statsMap = statsList.stream()
                .collect(Collectors.toMap(
                        map -> ((Number) map.get("project_group_id")).longValue(),
                        map -> map
                ));

        // 设置统计数据到响应对象
        for (ProjectGropRespVO projectGropRespVO : bean.getList()) {
            Map<String, Object> stats = statsMap.get(projectGropRespVO.getId());
            if (stats != null) {
                projectGropRespVO.setReceivedAmount(new BigDecimal(Optional.ofNullable(stats.get("received_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setPaidAmount(new BigDecimal(Optional.ofNullable(stats.get("paid_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setInvAmount(new BigDecimal(Optional.ofNullable(stats.get("inv_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setInvPayAmount(new BigDecimal(Optional.ofNullable(stats.get("inv_pay_amount"))
                        .map(val -> ((Number) val).doubleValue()).orElse(0.0)));
                projectGropRespVO.setEarliestContractYear((String) stats.get("earliest_contract_year"));
                projectGropRespVO.setWorkDay(Optional.ofNullable(stats.get("work_day"))
                        .map(val -> ((Number) val).intValue()).orElse(0));
                projectGropRespVO.setPersonCount(Optional.ofNullable(stats.get("person_count"))
                        .map(val -> ((Number) val).intValue()).orElse(0));
                projectGropRespVO.setOverFiveWanCount(Optional.ofNullable(stats.get("over_five_wan_count"))
                        .map(val -> ((Number) val).intValue()).orElse(0));
                Integer weeklyCount = Optional.ofNullable(stats.get("weekly_count"))
                        .map(val -> ((Number) val).intValue()).orElse(0);
                Integer unfinishedReceive = Optional.ofNullable(stats.get("unfinished_receive"))
                        .map(val -> ((Number) val).intValue()).orElse(0);
                Integer totalProjects = Optional.ofNullable(stats.get("total_projects"))
                        .map(val -> ((Number) val).intValue()).orElse(0);
                Integer unfinishedPay = Optional.ofNullable(stats.get("unfinished_pay"))
                        .map(val -> ((Number) val).intValue()).orElse(0);

                if (totalProjects > 0 && unfinishedReceive == 0 && unfinishedPay == 0) {
                    projectGropRespVO.setProjectGroupProgress("已完结");
                } else if (weeklyCount > 0) {
                    projectGropRespVO.setProjectGroupProgress("进行中");
                } else {
                    projectGropRespVO.setProjectGroupProgress("前期");
                }

                projectGropRespVO.setDepartment((String) stats.get("department"));

                String manager = Optional.ofNullable(projectGropRespVO.getProjectManager()).orElse("");
                String status = Optional.ofNullable((String) stats.get("manager_status"))
                        .map(s -> s.equals("审批通过") ? "已选任" : s)
                        .map(s -> "(" + s + ")")
                        .orElse("");
                projectGropRespVO.setProjectManager(manager + status);

                // 设置经办人信息
                projectGropRespVO.setHandler((String) stats.get("handler"));

                // 设置验收情况
                String reciAcceptanceStatus = (String) stats.get("reci_acceptance_status");
                String payAcceptanceStatus = (String) stats.get("pay_acceptance_status");

                // 设置收款项目验收状态
                projectGropRespVO.setReciAccepted(reciAcceptanceStatus);

                // 设置付款项目验收状态
                projectGropRespVO.setPayAccepted(payAcceptanceStatus);

                projectGropRespVO.setFlowId((String) stats.get("flow_id"));
            }

            // 设置当前登录用户部门ID
            projectGropRespVO.setDeptId(deptId != null ? deptId.intValue() : null);
        }

        // 设置创建者昵称
        setCreatorNicknames(bean.getList());

        return success(bean);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出项目组 Excel")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProjectGropExcel(@Valid ProjectGropPageReqVO pageReqVO,
                                       HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProjectGropDO> list = projectGropService.getProjectGropPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "项目组.xls", "数据", ProjectGropRespVO.class,
                BeanUtils.toBean(list, ProjectGropRespVO.class));
    }

    @GetMapping("/get-projects/{projectGroupCode}")
    @Operation(summary = "获得项目组下的项目列表")
    @Parameter(name = "projectGroupCode", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<ProjectRespVO>> getProjectsByGroupCode(@PathVariable("projectGroupCode") String projectGroupCode) {
        List<ProjectSaveReqVO> list = projectGropService.getProjectsByGroupCode(projectGroupCode);
        List<ProjectRespVO> result = BeanUtils.toBean(list, ProjectRespVO.class);

        // 逐个查询审批状态
        result.forEach(project -> {
            String status = myMapper.selectApprovalStatusByProjectId(project.getId());
            project.setApprovalStatus(status);
            project.setFlowId(myMapper.selectFlowIdByProjectId(project.getId()));
        });

        return success(result);
    }

    @GetMapping("/get-fees/{projectGroupCode}")
    @Operation(summary = "获得项目组下的费项列表")
    @Parameter(name = "projectGroupCode", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<FeeRespVO>> getFeesByGroupCode(@PathVariable("projectGroupCode") String projectGroupCode) {
        List<FeeSaveReqVO> list = projectGropService.getFeesByGroupCode(projectGroupCode);
        return success(BeanUtils.toBean(list, FeeRespVO.class));
    }

    @GetMapping("/get-fees-by-project/{projectCode}")
    @Operation(summary = "获得项目下的费项列表")
    @Parameter(name = "projectCode", description = "项目编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<FeeRespVO>> getFeesByProjectCode(@PathVariable("projectCode") String projectCode) {
        List<FeeSaveReqVO> list = projectGropService.getFeesByProjectCode(projectCode);
        return success(BeanUtils.toBean(list, FeeRespVO.class));
    }

    @GetMapping("/get-fees-by-project-new/{projectCode}")
    @Operation(summary = "获得项目下的费项列表")
    @Parameter(name = "projectCode", description = "项目编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<FeeRespVO>> getFeesByProjectCodeNew(@PathVariable("projectCode") String projectCode) {
        List<FeeSaveReqVO> list = projectGropService.getFeesByProjectCode(projectCode);
        List<FeeRespVO> feeRespList = BeanUtils.toBean(list, FeeRespVO.class);

        for (FeeRespVO fee : feeRespList) {
            // 获取关联的合同信息
            ContractDO contract = contractService.getContract(fee.getContractId());
            if (contract != null && contract.getPaymentRelationship() != null) {
                // 根据收付款关系计算实际金额
                if ("收".equals(contract.getPaymentRelationship())) {
                    fee.setActualAmount(myMapper.selectReceiveAmountByFeeId(fee.getId()));
                } else if ("付".equals(contract.getPaymentRelationship())) {
                    fee.setActualAmount(myMapper.selectPayAmountByFeeId(fee.getId()));
                }
            }
        }

        return success(feeRespList);
    }


    @GetMapping("/get-fees-by-contract/{contractId}")
    @Operation(summary = "获得合同下的费项列表")
    @Parameter(name = "contractId", description = "合同id", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<FeeRespVO>> getFeesByContractId(@PathVariable("contractId") String contractId) {
        List<FeeSaveReqVO> list = projectGropService.getFeeByContractId(contractId);
        return success(BeanUtils.toBean(list, FeeRespVO.class));
    }


    @GetMapping("/get-by-number/{projectGropNumber}")
    @Operation(summary = "根据编号获得项目组")
    @Parameter(name = "projectGropNumber", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<ProjectGropRespVO> getProjectGropByNumber(@PathVariable("projectGropNumber") String projectGropNumber) {
        ProjectGropDO projectGrop = projectGropService.getProjectGropByNumber(projectGropNumber);
        ProjectGropRespVO respVO = BeanUtils.toBean(projectGrop, ProjectGropRespVO.class);

        // 设置创建者昵称
        if (respVO.getCreator() != null) {
            AdminUserDO user = adminUserService.getUser(Long.valueOf(respVO.getCreator()));
            if (user != null) {
                respVO.setCreatorNickname(user.getNickname());
            }
        }

        return success(respVO);
    }

    @GetMapping("/receivable-projects/{projectGropNumber}")
    @Operation(summary = "获得项目组的收款项目及费项")
    @Parameter(name = "projectGropNumber", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<ProjectWithFeesRespVO>> getReceivableProjects(@PathVariable("projectGropNumber") String projectGropNumber) {
        List<ProjectWithFeesRespVO> list = projectGropService.getReceivableProjects(projectGropNumber);
        for (ProjectWithFeesRespVO projectWithFeesRespVO : list) {
            // 注意：这里是针对具体项目的收付款明细，应该从fee_detail表获取
            projectWithFeesRespVO.setReceivedAmount(myMapper.selectSumDetailAmountByProjectId(projectWithFeesRespVO.getId(), "收款"));
            projectWithFeesRespVO.setPaidAmount(myMapper.selectSumDetailAmountByProjectId(projectWithFeesRespVO.getId(), "付款"));
            projectWithFeesRespVO.setInvAlreadyAmount(myMapper.selectSumDetailAmountByProjectId(projectWithFeesRespVO.getId(), "开票"));
            // 发票号码查询
            for (FeeRespVO fee : projectWithFeesRespVO.getFees()) {
                if (fee.getId() != null) {
                    List<String> invNos = myMapper.selectInvNosByFeeId(fee.getId());
                    fee.setInvNos(invNos);
                }
            }
        }
        return success(list);
    }

    @GetMapping("/payable-projects/{projectGropNumber}")
    @Operation(summary = "获得项目组的付款项目及费项")
    @Parameter(name = "projectGropNumber", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<List<ProjectWithFeesRespVO>> getPayableProjects(@PathVariable("projectGropNumber") String projectGropNumber) {
        List<ProjectWithFeesRespVO> list = projectGropService.getPayableProjects(projectGropNumber);
        for (ProjectWithFeesRespVO projectWithFeesRespVO : list) {
            // 注意：这里是针对具体项目的收付款明细，应该从fee_detail表获取
            projectWithFeesRespVO.setReceivedAmount(myMapper.selectSumDetailAmountByProjectId(projectWithFeesRespVO.getId(), "收款"));
            projectWithFeesRespVO.setPaidAmount(myMapper.selectSumDetailAmountByProjectId(projectWithFeesRespVO.getId(), "付款"));
            projectWithFeesRespVO.setInvAlreadyAmount(myMapper.selectSumDetailAmountByProjectId(projectWithFeesRespVO.getId(), "收票"));
            projectWithFeesRespVO.setApprovalStatus(myMapper.selectApprovalStatusByProjectId(projectWithFeesRespVO.getId()));
            projectWithFeesRespVO.setFlowId(myMapper.selectFlowIdByProjectId(projectWithFeesRespVO.getId()));
        }
        return success(list);
    }

    @GetMapping("/contract-amounts/{projectGropNumber}")
    @Operation(summary = "获取项目组的合同金额统计")
    @Parameter(name = "projectGropNumber", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<Map<String, BigDecimal>> getContractAmounts(@PathVariable("projectGropNumber") String projectGropNumber) {
        return success(projectGropService.getContractAmounts(projectGropNumber));
    }

    @GetMapping("/get-payment-amounts/{projectGropNumber}")
    @Operation(summary = "获取项目组的收付款总额")
    @Parameter(name = "projectGropNumber", description = "项目组编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:query')")
    public CommonResult<Map<String, BigDecimal>> getPaymentAmounts(@PathVariable("projectGropNumber") String projectGropNumber) {
        return success(projectGropService.getPaymentAmounts(projectGropNumber));
    }

    @PutMapping("/generate-number")
    @Operation(summary = "生成项目组编号")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:create')")
    public CommonResult<String> generateProjectGropNumber() {
        return success(projectGropService.generateProjectGropNumber());
    }

    @PostMapping("/calculate-coefficient")
    @Operation(summary = "自动计算项目系数")
    public CommonResult<BigDecimal> calculateProjectCoefficient(@Valid @RequestBody ProjectCoefficientCalculateReqVO reqVO) {
        BigDecimal coefficient = projectGropService.calculateProjectCoefficient(
                reqVO.getDeptId(),
                reqVO.getNature(),
                reqVO.getImportance(),
                reqVO.getReceivable(),  // 直接使用前端传入的收款金额（元）
                reqVO.getType()
        );
        return success(coefficient);
    }

    @GetMapping("/generate-number-appoint/{year}")
    @Operation(summary = "按指定年份生成项目组编号")
    @Transactional
    public CommonResult<String> generateAppointProjectGropNumber(@PathVariable("year") Long year) {
        System.out.println("指定年份是：" + year);
        return success(projectGropService.generateAppointProjectGropNumber(year));
    }

    @PostMapping("/createMergeGroup/{projectGroupName}")
//    @GetMapping("/createMergeGroup/{projectGroupName}")
    @Operation(summary = "合并项目组")
    @Parameter(name = "createMergeGroup", description = "创建合并项目组", required = true)
    public CommonResult<Long> createMergeGroup(@Valid @RequestBody List<ProjectGropSaveReqVO> selectedProjectGroup, @PathVariable("projectGroupName") String projectGroupName) {
//    public CommonResult<Long> createMergeGroup(@PathVariable("projectGroupName") String projectGroupName) {
        // 创建新项目组的准备文件
        System.out.println("新的项目组名称是:" + projectGroupName);
        System.out.println("选中的项目组有：" + selectedProjectGroup);
        ProjectGropSaveReqVO projectGroupRespVO = new ProjectGropSaveReqVO();
        // 修改新项目组的准备文件信息
        projectGroupRespVO.setProjectGropName(projectGroupName);
        projectGroupRespVO.setProjectGropNumber(projectGropService.generateProjectGropNumber());
        // 创建新项目组并返回新项目组id
        Long newProjectGroupId = projectGropService.createProjectGrop(projectGroupRespVO);
        // 查找选中的原项目组中对应的项目并修改保存
        for (ProjectGropSaveReqVO selectedGroup : selectedProjectGroup) {
            // 找出项目组名下的所有项目
            ProjectPageReqVO findProject = new ProjectPageReqVO();
            findProject.setProjectGroupId(selectedGroup.getId());
            List<ProjectDO> projectList = projectService.getProjectPage(findProject).getList();
            System.out.println("项目组名下的项目数量有：" + projectList.size());
            // 遍历项目并修改信息
            for (ProjectDO projectDO : projectList) {
                // 找出每个项目名下的所有计费记录
                FeePageReqVO checkFee = new FeePageReqVO();
                checkFee.setProjectGroupCode(selectedGroup.getProjectGropNumber());
                checkFee.setProjectCode(projectDO.getProjectCode());
                List<FeeDO> feeList = feeService.getFeePage(checkFee).getList();
                // 找出每个项目名下的所有计费详情记录
                FeeDetailPageReqVO checkFeeDetail = new FeeDetailPageReqVO();
                checkFeeDetail.setGroupCode(selectedGroup.getProjectGropNumber());
                checkFeeDetail.setProjectCode(projectDO.getProjectCode());
                List<FeeDetailDO> feeDetailList = feeDetailService.getFeeDetailPage(checkFeeDetail).getList();
                // 找出每个项目名下的所有开票记录
                InvMainPageReqVO checkInvMain = new InvMainPageReqVO();
                checkInvMain.setProjectGroupCode(selectedGroup.getProjectGropNumber());
                checkInvMain.setProjectCode(projectDO.getProjectCode());
                List<InvMainDO> invMainList = invMainService.getInvMainPage(checkInvMain).getList();

                // 修改项目的项目组id、编号、名称
                projectDO.setProjectGroupId(newProjectGroupId);
                projectDO.setProjectGroupCode(projectGroupRespVO.getProjectGropNumber());
                projectDO.setProjectGroupName(projectGroupName);
                // 获取新的项目编号并修改
                CommonResult<String> projectCodeResult = projectController.getProjectCode(projectGroupRespVO.getProjectGropNumber(), projectDO.getPayReciRelation());
                projectDO.setProjectCode(projectCodeResult.getData());
                // 将存储文件的数据类型转换成保存格式
                ProjectSaveReqVO projectSaveReqVO = BeanUtils.toBean(projectDO, ProjectSaveReqVO.class);
                System.out.println("将要存储的文件如下\n" + projectSaveReqVO);
                // 保存修改后的项目
                projectService.updateProject(projectSaveReqVO);

                // 修改合同的项目组id、编号、名称和项目编号
                // 找出每个项目之下的所有合同
                ContractPageReqVO checkContract = new ContractPageReqVO();
                checkContract.setProjectGroupId(selectedGroup.getId());
                checkContract.setProjectId(projectDO.getId());
                List<ContractDO> contractList = contractService.getContractPage(checkContract).getList();
                System.out.println("项目名下的合同数量是：" + contractList.size());
                // 修改每一个合同的项目组编号，项目组名称，项目组id和项目编号
                for (ContractDO contractDO : contractList) {
                    // 修改项目组id、编号、名称
                    contractDO.setProjectGroupId(newProjectGroupId);
                    contractDO.setProjectGroupCode(projectGroupRespVO.getProjectGropNumber());
                    contractDO.setProjectGroupName(projectGroupName);
                    // 修改项目编号
                    contractDO.setProjectCode(projectCodeResult.getData());
                    // 将存储文件的数据类型转换成保存格式
                    ContractSaveReqVO contractSaveReqVO = BeanUtils.toBean(contractDO, ContractSaveReqVO.class);
                    System.out.println("将要存储的文件如下\n" + contractSaveReqVO);
                    // 保存修改后的合同
                    contractService.updateContract(contractSaveReqVO);
                }

                // 修改计费的项目组编号和项目编号
                System.out.println("项目名下的计费数量是：" + feeList.size());
                // 修改每一个计费记录的项目组编号和项目编号
                for (FeeDO feeDO : feeList) {
                    // 修改项目组编号
                    feeDO.setProjectGroupCode(projectGroupRespVO.getProjectGropNumber());
                    // 修改项目编号
                    feeDO.setProjectCode(projectCodeResult.getData());
                    // 将存储文件数据类型转换成保存格式
                    FeeSaveReqVO feeSaveReqVO = BeanUtils.toBean(feeDO, FeeSaveReqVO.class);
                    System.out.println("将要存储的文件如下\n" + feeSaveReqVO);
                    // 保存修改后的计费记录
                    feeService.updateFee(feeSaveReqVO);
                }

                // 修改计费详情的项目组id、项目组编号、项目组名称和项目编号
                System.out.println("项目名下的计费详情数量是：" + feeDetailList.size());
                // 修改每一个计费详情记录的内容
                for (FeeDetailDO feeDetailDO : feeDetailList) {
                    // 修改项目组编号
                    feeDetailDO.setGroupCode(projectGroupRespVO.getProjectGropNumber());
                    // 修改项目组名称
                    feeDetailDO.setGroupName(projectGroupName);
                    // 修改项目组id
                    feeDetailDO.setGroupId(Math.toIntExact(newProjectGroupId));
                    // 修改项目编号
                    feeDetailDO.setProjectCode(projectCodeResult.getData());
                    // 将存储文件数据类型转换成保存格式
                    FeeDetailSaveReqVO feeDetailSaveReqVO = BeanUtils.toBean(feeDetailDO, FeeDetailSaveReqVO.class);
                    System.out.println("将要存储的文件如下\n" + feeDetailSaveReqVO);
                    // 保存修改后的计费详情记录
                    feeDetailService.updateFeeDetail(feeDetailSaveReqVO);
                }

                // 修改开票记录的项目组编号和项目编号
                System.out.println("项目名下的开票数量是：" + invMainList.size());
                for (InvMainDO invMainDO : invMainList) {
                    // 修改项目组编号
                    invMainDO.setProjectGroupCode(projectGroupRespVO.getProjectGropNumber());
                    // 修改项目编号
                    invMainDO.setProjectCode(projectCodeResult.getData());
                    // 将存储文件数据类型转换成保存格式
                    InvMainSaveReqVO invMainSaveReqVO = BeanUtils.toBean(invMainDO, InvMainSaveReqVO.class);
                    System.out.println("将要存储的文件如下\n" + invMainSaveReqVO);
                    // 保存修改后的开票记录
                    invMainService.updateInvMain(invMainSaveReqVO);
                }
            }
        }
        // 删除原来选中的项目组记录
        for (ProjectGropSaveReqVO selectedGroup : selectedProjectGroup) {
            projectGropService.deleteProjectGrop(selectedGroup.getId());
        }
        // 返回成功讯号
        return success(newProjectGroupId);
//        return success(null);
    }

    @PutMapping("/percentage")
    @Operation(summary = "更新项目组进度")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:update')")
    public CommonResult<Boolean> updateProjectGropPercentage(@Valid @RequestBody ProjectGropSaveReqVO updateReqVO) {
        projectGropService.updateProjectGrop(updateReqVO);
        return success(true);
    }

    @PutMapping("/projectManage")
    @Operation(summary = "更新项目组项目经理")
//    @PreAuthorize("@ss.hasPermission('projectmanage:project-grop:update')")
    public CommonResult<Boolean> updateProjectGropManager(@Valid @RequestBody ProjectGropSaveReqVO updateReqVO) {
        ProjectGropDO projectGrop = projectGropService.getProjectGrop(updateReqVO.getId());
        if (projectGrop != null && "1".equals(projectGrop.getApproved())) {
            return CommonResult.error(400, "该项目组项目经理已审批通过，不可修改");
        }
        projectGropService.updateProjectGrop(updateReqVO);
        return success(true);
    }

//    @GetMapping("/list")
//    @Operation(summary = "获得项目经理审批列表")
//    public CommonResult<ProjectManagerApprovalRespVO> getProjectManagerApprovalList(@RequestParam("id") Long id) {
//        ProjectGropDO projectGrop = projectGropService.getProjectGrop(id);
//        System.out.println("项目组信息为：" + projectGrop);
//        System.out.println(id);
//        ProjectManagerApprovalSaveReqVO projectManagerApprovalSaveReqVO = BeanUtils.toBean(projectGrop, ProjectManagerApprovalSaveReqVO.class);
//        projectManagerApprovalSaveReqVO.setDocCreator(SecurityFrameworkUtils.getLoginUser().getInfo().get("nickname"));// 当前登录用户
//        projectManagerApprovalSaveReqVO.setProjectManager(SecurityFrameworkUtils.getLoginUser().getInfo().get("nickname"));// 当前登录用户
//        //  projectManagerApprovalSaveReqVO.setPhone(mymapper.selectUsernameById(SecurityFrameworkUtils.getLoginUser().getId()));
//        projectManagerApprovalSaveReqVO.setProjectName(projectGrop.getProjectGropName());
//
//        // projectManagerApprovalSaveReqVO.setProjectCatagory(project.getType());
////        projectManagerApprovalSaveReqVO.setEstimateMoney(project.getTotalAmount());
////       projectManagerApprovalSaveReqVO.setSource(project.getPayerName());
//        projectManagerApprovalSaveReqVO.setDocSubject( projectGrop.getProjectGropName());
//        projectManagerApprovalSaveReqVO.setProjectGropId(projectGrop.getId());
//        //projectManagerApprovalSaveReqVO.setApprovalForm("广州港数据科技有限公司项目经理审批");
//        //projectManagerApprovalSaveReqVO.setFdTemplataId(Long.valueOf("18995d54944d66182b679df43ebb6f68\n"));
//        projectManagerApprovalSaveReqVO.setId(null);
//        System.out.println(projectManagerApprovalSaveReqVO);
//        System.out.println(projectGrop);
//        return success(BeanUtils.toBean(projectManagerApprovalSaveReqVO, ProjectManagerApprovalRespVO.class));
//    }

    @GetMapping("/get-report-list-by-project")
    @Operation(summary = "获取项目阶段汇报信息")
    public CommonResult<List<ReportRespVO>> getReportListByProject(
            @RequestParam(value = "projectId", required = false) String projectId,
            @RequestParam("projectGroupId") String projectGroupId) {

        List<ReportDO> reportList;

        // 根据参数情况决定查询方式
        if (projectId != null && !projectId.isEmpty()) {
            reportList = reportService.getReportListByProjectIdAndGroupId(projectId, projectGroupId);
        } else {
            reportList = reportService.getReportListByProjectIdAndGroupId("", projectGroupId);
        }

        // 转换并返回数据
        return success(BeanUtils.toBean(reportList, ReportRespVO.class));
    }

    /**
     * 权限检查结果类
     */
    private static class PermissionCheckResult {
        private boolean hasPermission;
        private boolean hasFullAccess;
        private String projectId;
        private String systemId;

        public PermissionCheckResult(boolean hasPermission, boolean hasFullAccess, String projectId, String systemId) {
            this.hasPermission = hasPermission;
            this.hasFullAccess = hasFullAccess;
            this.projectId = projectId;
            this.systemId = systemId;
        }

        public boolean hasPermission() {
            return hasPermission;
        }

        public boolean hasFullAccess() {
            return hasFullAccess;
        }

        public String getProjectId() {
            return projectId;
        }

        public String getSystemId() {
            return systemId;
        }
    }

    /**
     * 检查用户对项目组的权限
     *
     * @param projectGroupId     项目组ID
     * @param requestedProjectId 请求的项目ID（可选）
     * @param requestedSystemId  请求的系统ID（可选）
     * @return 权限检查结果
     */
    private PermissionCheckResult checkProjectGroupPermission(String projectGroupId, String requestedProjectId, String requestedSystemId) {
        // 获取当前登录用户名
        final String loginUsername;
        final Long loginUserId;
        try {
            loginUsername = SecurityFrameworkUtils.getLoginUser().getInfo().get("nickname");
            loginUserId = SecurityFrameworkUtils.getLoginUserId();
        } catch (Exception e) {
            // 如果获取用户信息失败，默认无权限
            return new PermissionCheckResult(false, false, requestedProjectId, requestedSystemId);
        }

        if (loginUsername == null || loginUsername.isEmpty() || loginUserId == null) {
            return new PermissionCheckResult(false, false, requestedProjectId, requestedSystemId);
        }

        // 检查用户是否具有管理角色
        boolean hasManagementRole = false;
        try {
            // 获取用户的角色信息
            List<Map<String, Object>> userRoles = myMapper.selectCurrentUserRoles(loginUserId);

            // 检查是否具有管理角色（角色名包含"经理"、"管理员"或"领导"）
            for (Map<String, Object> role : userRoles) {
                String roleName = (String) role.get("roleName");
                if (roleName != null && (roleName.contains("经理") ||
                        roleName.contains("管理员") ||
                        roleName.contains("领导"))) {
                    hasManagementRole = true;
                    break;
                }
            }

            // 如果用户具有管理角色，赋予完全访问权限
            if (hasManagementRole || "超级管理员".equals(loginUsername)) {
                return new PermissionCheckResult(true, true, requestedProjectId, requestedSystemId);
            }
        } catch (Exception e) {
            // 角色检查出错，继续常规权限检查流程
            System.out.println("角色检查出错: " + e.getMessage());
        }

        // 检查是否为项目组管理者
        ProjectGropDO projectGroup = projectGropService.getProjectGrop(Long.parseLong(projectGroupId));
        if (projectGroup != null && (loginUsername.equals(projectGroup.getProjectManager()))) {
            // 是项目组管理者，拥有全部权限
            return new PermissionCheckResult(true, true, requestedProjectId, requestedSystemId);
        }

        // 检查是否为项目管理者
        // 获取项目列表并转换为ProjectDO类型
        ProjectPageReqVO reqVO = new ProjectPageReqVO();
        reqVO.setProjectGroupId(Long.parseLong(projectGroupId));
        List<ProjectDO> projectList = projectService.getProjectPage(reqVO).getList();

        boolean isProjectManager = projectList.stream()
                .anyMatch(project -> loginUsername.equals(project.getProjectManagerName()));

        if (isProjectManager) {
            // 是项目管理者，拥有全部权限
            return new PermissionCheckResult(true, true, requestedProjectId, requestedSystemId);
        }

        // 检查是否为项目组成员
        List<ProjectPersonDO> projectPersonList = projectPersonService.getProjectPersonListByProjectGroupId(projectGroupId);
        boolean isProjectMember = projectPersonList.stream()
                .anyMatch(person -> loginUsername.equals(person.getName()));

        if (isProjectMember) {
            // 是项目组成员，拥有有限权限，只能查看自己参与的项目数据
            // 查找该成员参与的项目
            ProjectPersonDO memberProject = projectPersonList.stream()
                    .filter(person -> loginUsername.equals(person.getName()))
                    .findFirst()
                    .orElse(null);

            if (memberProject != null) {
                String memberProjectId = String.valueOf(memberProject.getProjectId());
                String memberSystemId = memberProject.getSystemId();

                // 如果用户请求了特定项目，但不是他参与的项目，则拒绝访问
                if (requestedProjectId != null && !requestedProjectId.isEmpty() &&
                        !requestedProjectId.equals(memberProjectId)) {
                    return new PermissionCheckResult(false, false, requestedProjectId, requestedSystemId);
                }

                // 如果用户请求了特定系统，但不是他参与的系统，则拒绝访问
                if (requestedSystemId != null && !requestedSystemId.isEmpty() &&
                        memberSystemId != null && !requestedSystemId.equals(memberSystemId)) {
                    return new PermissionCheckResult(false, false, requestedProjectId, requestedSystemId);
                }

                // 返回有限权限，并设置为成员的项目和系统
                return new PermissionCheckResult(true, false, memberProjectId, memberSystemId);
            }
        }

        // 默认无权限
        return new PermissionCheckResult(false, false, requestedProjectId, requestedSystemId);
    }

    @GetMapping("/get-stage-info")
    @Operation(summary = "获取项目所有阶段信息")
    public CommonResult<Map<String, Object>> getStageInfo(
            @RequestParam(value = "projectId", required = false) String projectId,
            @RequestParam(value = "projectGroupId", required = false) String projectGroupId,
            @RequestParam(value = "systemId", required = false) String systemId) {

        // 权限检查
        PermissionCheckResult permissionResult = checkProjectGroupPermission(projectGroupId, projectId, systemId);

        // 如果没有权限，直接返回错误
        if (!permissionResult.hasPermission()) {
            return null;
        }

        // 如果有限制权限，使用权限检查返回的参数
        if (!permissionResult.hasFullAccess()) {
            projectId = permissionResult.getProjectId();
            systemId = permissionResult.getSystemId();
        }

        Map<String, Object> allStagesData = new HashMap<>();

        try {
            // 0.获取前期阶段的数据
            List<EarlyDO> earlyList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    List<EarlyDO> earlyDOList = earlyService.getEarlyListBypidpgid(projectId, projectGroupId, systemId);
                    earlyList.addAll(earlyDOList);

                    List<EarlyDO> nullearlyDOList = earlyService.getEarlyListBypidpgidwithoutsystemid(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullearlyDOList != null) {
                        nullearlyDOList.stream()
                                .filter(early -> early.getSystemId() == null || early.getSystemId().isEmpty())
                                .forEach(earlyList::add);
                    }
                } else {
                    earlyList = earlyService.getEarlyListBypidpgidwithoutsystemid(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的汇报数据
                    List<EarlyDO> systemReportList = earlyService.getEarlyListBypidpgid("", projectGroupId, systemId);
                    earlyList.addAll(systemReportList);

                    // 获取相同项目组但没有指定系统ID的汇报数据
                    List<EarlyDO> nullSystemReportList = earlyService.getEarlyListBypidpgidwithoutsystemid("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemReportList != null) {
                        nullSystemReportList.stream()
                                .filter(early -> early.getSystemId() == null || early.getSystemId().isEmpty())
                                .forEach(earlyList::add);
                    }
                } else {
                    earlyList = earlyService.getEarlyListBypidpgidwithoutsystemid("", projectGroupId);
                }
            }
            CommonResult<List<EarlyRespVO>> earlyResult = success(BeanUtils.toBean(earlyList, EarlyRespVO.class));
            earlyResult.setMsg("数据来源于前期阶段");
            allStagesData.put("early", earlyResult);

            // 1. 获取汇报阶段数据
            List<ReportDO> reportList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的汇报数据
                    List<ReportDO> systemReportList = reportService.getReportListByProjectIdAndGroupId(projectId, projectGroupId, systemId);
                    reportList.addAll(systemReportList);

                    // 获取相同项目和项目组但没有指定系统ID的汇报数据
                    List<ReportDO> nullSystemReportList = reportService.getReportListByProjectIdAndGroupId(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemReportList != null) {
                        nullSystemReportList.stream()
                                .filter(report -> report.getSystemId() == null || report.getSystemId().isEmpty())
                                .forEach(reportList::add);
                    }
                } else {
                    reportList = reportService.getReportListByProjectIdAndGroupId(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的汇报数据
                    List<ReportDO> systemReportList = reportService.getReportListByProjectIdAndGroupId("", projectGroupId, systemId);
                    reportList.addAll(systemReportList);

                    // 获取相同项目组但没有指定系统ID的汇报数据
                    List<ReportDO> nullSystemReportList = reportService.getReportListByProjectIdAndGroupId("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemReportList != null) {
                        nullSystemReportList.stream()
                                .filter(report -> report.getSystemId() == null || report.getSystemId().isEmpty())
                                .forEach(reportList::add);
                    }
                } else {
                    reportList = reportService.getReportListByProjectIdAndGroupId("", projectGroupId);
                }
            }
            CommonResult<List<ReportRespVO>> reportResult = success(BeanUtils.toBean(reportList, ReportRespVO.class));
            reportResult.setMsg("数据来源于汇报阶段");
            allStagesData.put("report", reportResult);

            // 2. 获取方案阶段数据
            List<PlanDO> planList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的方案数据
                    List<PlanDO> systemPlanList = planService.getPlanListByProjectIdAndGroupId(projectId, projectGroupId, systemId);
                    planList.addAll(systemPlanList);

                    // 获取相同项目和项目组但没有指定系统ID的方案数据
                    List<PlanDO> nullSystemPlanList = planService.getPlanListByProjectIdAndGroupId(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemPlanList != null) {
                        nullSystemPlanList.stream()
                                .filter(plan -> plan.getSystemId() == null || plan.getSystemId().isEmpty())
                                .forEach(planList::add);
                    }
                } else {
                    planList = planService.getPlanListByProjectIdAndGroupId(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的方案数据
                    List<PlanDO> systemPlanList = planService.getPlanListByProjectIdAndGroupId("", projectGroupId, systemId);
                    planList.addAll(systemPlanList);

                    // 获取相同项目组但没有指定系统ID的方案数据
                    List<PlanDO> nullSystemPlanList = planService.getPlanListByProjectIdAndGroupId("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemPlanList != null) {
                        nullSystemPlanList.stream()
                                .filter(plan -> plan.getSystemId() == null || plan.getSystemId().isEmpty())
                                .forEach(planList::add);
                    }
                } else {
                    planList = planService.getPlanListByProjectIdAndGroupId("", projectGroupId);
                }
            }
            CommonResult<List<PlanRespVO>> planResult = success(BeanUtils.toBean(planList, PlanRespVO.class));
            planResult.setMsg("数据来源于方案阶段");
            allStagesData.put("plan", planResult);

            // 3. 获取合同阶段数据
            CommonResult<?> contractResult;
            if (projectId != null && !projectId.isEmpty()) {
                contractResult = contractController.getContractByprojectId(projectId);
                contractResult.setMsg("数据来源于合同阶段 - 项目级别");
            } else {
                contractResult = contractController.getContractByprojectGroup(projectGroupId);
                contractResult.setMsg("数据来源于合同阶段 - 项目组级别");
            }
            allStagesData.put("contract", contractResult);

            // 4. 获取实施阶段数据
            List<ImplementDO> implementList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的实施数据
                    List<ImplementDO> systemImplementList = implementService.getimplementListBypidpgid(projectId, projectGroupId, systemId);
                    implementList.addAll(systemImplementList);

                    // 获取相同项目和项目组但没有指定系统ID的实施数据
                    List<ImplementDO> nullSystemImplementList = implementService.getimplementListBypidpgid(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemImplementList != null) {
                        nullSystemImplementList.stream()
                                .filter(implement -> implement.getSystemId() == null || implement.getSystemId().isEmpty())
                                .forEach(implementList::add);
                    }
                } else {
                    implementList = implementService.getimplementListBypidpgid(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的实施数据
                    List<ImplementDO> systemImplementList = implementService.getimplementListBypidpgid("", projectGroupId, systemId);
                    implementList.addAll(systemImplementList);

                    // 获取相同项目组但没有指定系统ID的实施数据
                    List<ImplementDO> nullSystemImplementList = implementService.getimplementListBypidpgid("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemImplementList != null) {
                        nullSystemImplementList.stream()
                                .filter(implement -> implement.getSystemId() == null || implement.getSystemId().isEmpty())
                                .forEach(implementList::add);
                    }
                } else {
                    implementList = implementService.getimplementListBypidpgid("", projectGroupId);
                }
            }
            CommonResult<List<ImplementRespVO>> implementResult = success(BeanUtils.toBean(implementList, ImplementRespVO.class));
            implementResult.setMsg("数据来源于实施阶段");
            allStagesData.put("implement", implementResult);

            // 5. 获取上线阶段数据
            List<LaunchDO> launchList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的上线数据
                    List<LaunchDO> systemLaunchList = launchService.getLaunchListByProjectIdAndGroupId(projectId, projectGroupId, systemId);
                    launchList.addAll(systemLaunchList);

                    // 获取相同项目和项目组但没有指定系统ID的上线数据
                    List<LaunchDO> nullSystemLaunchList = launchService.getLaunchListByProjectIdAndGroupId(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemLaunchList != null) {
                        nullSystemLaunchList.stream()
                                .filter(launch -> launch.getSystemId() == null || launch.getSystemId().isEmpty())
                                .forEach(launchList::add);
                    }
                } else {
                    launchList = launchService.getLaunchListByProjectIdAndGroupId(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的上线数据
                    List<LaunchDO> systemLaunchList = launchService.getLaunchListByProjectIdAndGroupId("", projectGroupId, systemId);
                    launchList.addAll(systemLaunchList);

                    // 获取相同项目组但没有指定系统ID的上线数据
                    List<LaunchDO> nullSystemLaunchList = launchService.getLaunchListByProjectIdAndGroupId("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemLaunchList != null) {
                        nullSystemLaunchList.stream()
                                .filter(launch -> launch.getSystemId() == null || launch.getSystemId().isEmpty())
                                .forEach(launchList::add);
                    }
                } else {
                    launchList = launchService.getLaunchListByProjectIdAndGroupId("", projectGroupId);
                }
            }
            CommonResult<List<LaunchRespVO>> launchResult = success(BeanUtils.toBean(launchList, LaunchRespVO.class));
            launchResult.setMsg("数据来源于上线阶段");
            allStagesData.put("launch", launchResult);

            // 6. 获取验收阶段数据
            CommonResult<List<List<AcceptanceDO>>> acceptanceResult = acceptanceController.getAcceptanceList(projectId, projectGroupId, systemId);
            acceptanceResult.setMsg("数据来源于验收阶段");
            allStagesData.put("acceptance", acceptanceResult);

            // 7. 获取周报阶段数据
            List<WeeklyDO> weeklyList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的周报
                    List<WeeklyDO> systemWeeklyList = weeklyService.getReportListByProjectIdAndGroupId(projectId, projectGroupId, systemId);
                    weeklyList.addAll(systemWeeklyList);

                    // 获取相同项目和项目组但没有指定系统ID的周报
                    List<WeeklyDO> nullSystemWeeklyList = weeklyService.getReportListByProjectIdAndGroupId(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemWeeklyList != null) {
                        nullSystemWeeklyList.stream()
                                .filter(weekly -> weekly.getSystemId() == null || weekly.getSystemId().isEmpty())
                                .forEach(weeklyList::add);
                    }
                } else {
                    weeklyList = weeklyService.getReportListByProjectIdAndGroupId(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的周报
                    List<WeeklyDO> systemWeeklyList = weeklyService.getReportListByProjectIdAndGroupId("", projectGroupId, systemId);
                    weeklyList.addAll(systemWeeklyList);

                    // 获取相同项目组但没有指定系统ID的周报
                    List<WeeklyDO> nullSystemWeeklyList = weeklyService.getReportListByProjectIdAndGroupId("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemWeeklyList != null) {
                        nullSystemWeeklyList.stream()
                                .filter(weekly -> weekly.getSystemId() == null || weekly.getSystemId().isEmpty())
                                .forEach(weeklyList::add);
                    }
                } else {
                    weeklyList = weeklyService.getReportListByProjectIdAndGroupId("", projectGroupId);
                }
            }
            CommonResult<List<WeeklyRespVO>> weeklyResult = success(BeanUtils.toBean(weeklyList, WeeklyRespVO.class));
            weeklyResult.setMsg("数据来源于周报阶段");
            allStagesData.put("weekly", weeklyResult);

            // 8. 获取运维阶段数据
            List<ProjectManagerMaintenanceDO> maintenanceList = new ArrayList<>();
            if (projectId != null && !projectId.isEmpty()) {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的运维数据
                    List<ProjectManagerMaintenanceDO> systemMaintenanceList = projectManagerMaintenanceService.getMaintenanceListByProjectIdAndGroupId(projectId, projectGroupId, systemId);
                    maintenanceList.addAll(systemMaintenanceList);

                    // 获取相同项目和项目组但没有指定系统ID的运维数据
                    List<ProjectManagerMaintenanceDO> nullSystemMaintenanceList = projectManagerMaintenanceService.getMaintenanceListByProjectIdAndGroupId(projectId, projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemMaintenanceList != null) {
                        nullSystemMaintenanceList.stream()
                                .filter(maintenance -> maintenance.getSystemId() == null || maintenance.getSystemId().isEmpty())
                                .forEach(maintenanceList::add);
                    }
                } else {
                    maintenanceList = projectManagerMaintenanceService.getMaintenanceListByProjectIdAndGroupId(projectId, projectGroupId);
                }
            } else {
                if (systemId != null && !systemId.isEmpty()) {
                    // 获取特定系统的运维数据
                    List<ProjectManagerMaintenanceDO> systemMaintenanceList = projectManagerMaintenanceService.getMaintenanceListByProjectIdAndGroupId("", projectGroupId, systemId);
                    maintenanceList.addAll(systemMaintenanceList);

                    // 获取相同项目组但没有指定系统ID的运维数据
                    List<ProjectManagerMaintenanceDO> nullSystemMaintenanceList = projectManagerMaintenanceService.getMaintenanceListByProjectIdAndGroupId("", projectGroupId);
                    // 过滤出systemId为null的数据
                    if (nullSystemMaintenanceList != null) {
                        nullSystemMaintenanceList.stream()
                                .filter(maintenance -> maintenance.getSystemId() == null || maintenance.getSystemId().isEmpty())
                                .forEach(maintenanceList::add);
                    }
                } else {
                    maintenanceList = projectManagerMaintenanceService.getMaintenanceListByProjectIdAndGroupId("", projectGroupId);
                }
            }
            CommonResult<List<ProjectManagerMaintenanceRespVO>> maintenanceResult = success(BeanUtils.toBean(maintenanceList, ProjectManagerMaintenanceRespVO.class));
            maintenanceResult.setMsg("数据来源于运维阶段");
            allStagesData.put("maintenance", maintenanceResult);

            // 添加系统视图标识
            if (systemId != null && !systemId.isEmpty()) {
                allStagesData.put("isSystemView", true);
                allStagesData.put("systemId", systemId);
            } else {
                allStagesData.put("isSystemView", false);
            }

            return success(allStagesData);
        } catch (Exception e) {
            e.printStackTrace();
            return success(allStagesData);
        }
    }

    @GetMapping("/getNew")
    @Operation(summary = "获得项目组")
    public CommonResult<ProjectGropRespVO> getProjectGropNew(@RequestParam("id") Long id) {
        ProjectGropDO projectGrop = projectGropService.getProjectGrop(id);
        ProjectGropRespVO respVO = BeanUtils.toBean(projectGrop, ProjectGropRespVO.class);

        // 新增验收状态判断
        ProjectPageReqVO req = new ProjectPageReqVO();
        req.setProjectGroupId(id);
        PageResult<ProjectDO> projects = projectService.getProjectPage(req);
        //判断项目组下是否有项目，如果有，判断是否全部验收
        boolean hasProjects = !projects.getList().isEmpty();
        boolean allAccepted = projects.getList().stream()
                .allMatch(p -> "是".equals(p.getIfAccepted()));

        respVO.setStatusText(hasProjects && allAccepted ? "已验收" : "进行中");

        // 设置创建者昵称
        if (respVO.getCreator() != null && !"".equals(respVO.getCreator())) {
            AdminUserDO user = adminUserService.getUser(Long.valueOf(respVO.getCreator()));
            if (user != null) {
                respVO.setCreatorNickname(user.getNickname());
            }
        }

        return success(respVO);
    }

    @GetMapping("/get-project-documents")
    @Operation(summary = "获取项目相关文档")
    public CommonResult<Map<String, List<Map<String, Object>>>> getProjectDocuments(
            @RequestParam(value = "projectId", required = false) String projectId,
            @RequestParam(value = "projectGroupId", required = false) String projectGroupId) {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();

        try {
            // 1. 获取合同文档
            List<Map<String, Object>> contractDocs = myMapper.selectContractDocumentsByProjectIdAndGroupId(projectId, projectGroupId);
            result.put("contract", contractDocs);

            // 2. 获取运维文档
            List<Map<String, Object>> maintenanceDocs = myMapper.selectMaintenanceDocumentsByProjectIdAndGroupId(projectId, projectGroupId);
            result.put("maintenance", maintenanceDocs);

            // 3. 获取上线文档
            List<Map<String, Object>> launchDocs = myMapper.selectLaunchDocumentsByProjectIdAndGroupId(projectId, projectGroupId);
            result.put("launch", launchDocs);

            // 4. 获取实施文档
            List<Map<String, Object>> implementDocs = myMapper.selectImplementDocumentsByProjectIdAndGroupId(projectId, projectGroupId);
            result.put("implement", implementDocs);

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return error(500, "获取项目文档失败: " + e.getMessage());
        }
    }

    @GetMapping("/get-project-system-list")
    @Operation(summary = "获取项目组下的项目和系统列表")
    public CommonResult<List<Map<String, Object>>> getProjectSystemList(@RequestParam("projectGropId") Long projectGropId) {
        List<Map<String, Object>> result = projectGropService.getProjectSystemList(projectGropId);
        return success(result);
    }

    @GetMapping("/by-department")
    @Operation(summary = "按部门查询项目组")
    @Parameter(name = "department", description = "部门名称", required = true)
    public CommonResult<PageResult<ProjectGropRespVO>> getProjectGropByDepartment(
            @RequestParam("department") String department,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "50") Integer pageSize) {
        // 获取当前登录用户的部门ID--暂时取消权限限制
        // Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        // // 权限检查：只有公司领导(197)和业务拓展部(198)可以使用部门搜索功能
        // if (deptId == null || (deptId != 197L && deptId != 198L && deptId != 115L)) {
        //     // 返回空结果集和权限提示
        //     return success(new PageResult<ProjectGropRespVO>(Collections.emptyList(), 0L).setTotal(0L))
        //             .setMsg("您没有权限使用部门搜索功能");
        // }

        // 创建分页参数
        ProjectGropPageReqVO pageReqVO = new ProjectGropPageReqVO();
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);

        // 调用Service层方法
        PageResult<ProjectGropDO> pageResult = projectGropService.getProjectGropByDepartment(department, pageReqVO);
        PageResult<ProjectGropRespVO> bean = BeanUtils.toBean(pageResult, ProjectGropRespVO.class);

        // 处理附加数据，与getProjectGropPage方法类似
        for (ProjectGropRespVO projectGropRespVO : bean.getList()) {
            projectGropRespVO.setReceivedAmount(myMapper.selectSumDetailAmountByProjectGroupCode(projectGropRespVO.getProjectGropNumber(), "收款"));
            projectGropRespVO.setPaidAmount(myMapper.selectSumDetailAmountByProjectGroupCode(projectGropRespVO.getProjectGropNumber(), "付款"));
            projectGropRespVO.setInvAmount(myMapper.selectSumDetailAmountByProjectGroupCode(projectGropRespVO.getProjectGropNumber(), "开票"));
            projectGropRespVO.setInvPayAmount(myMapper.selectSumDetailAmountByProjectGroupCode(projectGropRespVO.getProjectGropNumber(), "收票"));
            projectGropRespVO.setEarliestContractYear(myMapper.selectEarliestContractYearByGroupId(projectGropRespVO.getId()));
            projectGropRespVO.setWorkDay(myMapper.selectWorkDayByGroupId(projectGropRespVO.getId()));
            projectGropRespVO.setPersonCount(myMapper.selectPersonCountByGroupId(projectGropRespVO.getId()));
            projectGropRespVO.setOverFiveWanCount(myMapper.selectCountOverFiveWanByGroupId(projectGropRespVO.getId()));
            projectGropRespVO.setFlowId(myMapper.selectFlowIdByProjectGroupId(projectGropRespVO.getId()));
            projectGropRespVO.setStatusText(myMapper.selectStatusTextByProjectGroupId(projectGropRespVO.getId()));
            //Optional.ofNullable(projectGropMapper.selectTypeByGroupId(projectGropRespVO.getId()).getType()).ifPresent(projectGropRespVO::setType);
            Optional<ProjectGropDO> optionalProjectGropDO = Optional.ofNullable(projectGropMapper.selectTypeByGroupId(projectGropRespVO.getId()));
            List<String> typeList = optionalProjectGropDO.map(ProjectGropDO::getType).orElse(Collections.emptyList());
            System.out.println("typeList是这个鬼东西 " +projectGropRespVO.getId()+ typeList);
            Optional.ofNullable(typeList).filter(typeList1 -> !typeList1.isEmpty()).ifPresent(projectGropRespVO::setType);
            Integer weeklyCount = myMapper.selectWeeklyCountByProjectGroupId(projectGropRespVO.getId());
            Integer unfinishedReceive = myMapper.selectUnfinishedReceiveProjects(projectGropRespVO.getId());
            Integer totalProjects = myMapper.selectTotalProjectsByGroupId(projectGropRespVO.getId());
            Integer unfinishedPay = myMapper.selectUnfinishedPayProjects(projectGropRespVO.getId());

            if (totalProjects > 0 && unfinishedReceive == 0 && unfinishedPay == 0) {
                projectGropRespVO.setProjectGroupProgress("已完结");
            } else if (weeklyCount != null && weeklyCount > 0) {
                projectGropRespVO.setProjectGroupProgress("进行中");
            } else {
                projectGropRespVO.setProjectGroupProgress("前期");
            }
            projectGropRespVO.setDepartment(myMapper.selectManagingDept(projectGropRespVO.getId()));
            String manager = Optional.ofNullable(projectGropRespVO.getProjectManager()).orElse("");
            String status = Optional.ofNullable(myMapper.selectStatusByProjectGroupId(projectGropRespVO.getId()))
                    .map(s -> s.equals("审批通过") ? "已选任" : s)
                    .map(s -> "(" + s + ")")
                    .orElse("");
            projectGropRespVO.setProjectManager(manager + status);
        }

        // 设置创建者昵称
        setCreatorNicknames(bean.getList());

        return success(bean);
    }

    @GetMapping("/status")
    public CommonResult getPhone(@RequestParam Long projectGropId) {
        String status = myMapper.selectStatusByProjectGroupId(projectGropId);
        return CommonResult.success(status);
    }

    @GetMapping("/check-permission")
    @Operation(summary = "检查用户对项目组的权限")
    public CommonResult<ProjectGropPermissionRespVO> checkPermission(
            @RequestParam("projectGroupId") String projectGroupId,
            @RequestParam(value = "projectId", required = false) String projectId,
            @RequestParam(value = "systemId", required = false) String systemId) {

        // 调用现有的权限检查方法
        PermissionCheckResult result = checkProjectGroupPermission(projectGroupId, projectId, systemId);

        // 转换为API响应对象
        ProjectGropPermissionRespVO respVO = new ProjectGropPermissionRespVO();
        respVO.setHasPermission(result.hasPermission());
        respVO.setHasFullAccess(result.hasFullAccess());
        respVO.setProjectId(result.getProjectId());
        respVO.setSystemId(result.getSystemId());

        // 根据是否有限权限判断是否为项目成员
        respVO.setIsProjectMember(result.hasPermission() && !result.hasFullAccess());

        return success(respVO);
    }

    @PostMapping("/updateOtherTables/{oldProjectGropNumber}/{projectGropNumber}")
    @Operation(summary = "更新项目组所牵连的其他表的信息")
    @Parameter(name = "updateOtherTables", description = "在项目组编号发生更新的时候，对关联的其他的表进行数据修正", required = true)
    public CommonResult<Boolean> updateOtherTables(@PathVariable("oldProjectGropNumber") String oldProjectGropNumber, @PathVariable("projectGropNumber") String projectGropNumber) {
        // 根据id查出对应项目组的原项目组编号
        ProjectGropDO projectGropDO = projectGropService.getProjectGropByNumber(oldProjectGropNumber);
        // 根据原项目组编号查出名下所有的项目记录
        ProjectPageReqVO checkProject = new ProjectPageReqVO();
        checkProject.setProjectGroupCode(oldProjectGropNumber);
        List<ProjectDO> projectDOList = projectService.getProjectPage(checkProject).getList();
        for (ProjectDO projectDO : projectDOList) {
            // 找出项目名下的所有合同记录
            ContractPageReqVO checkContract = new ContractPageReqVO();
            checkContract.setProjectCode(projectDO.getProjectCode());
            checkContract.setProjectGroupCode(oldProjectGropNumber);
            List<ContractDO> contractDOList = contractService.getContractPage(checkContract).getList();
            // 找出项目名下的所有计费记录
            FeePageReqVO checkFee = new FeePageReqVO();
            checkFee.setProjectCode(projectDO.getProjectCode());
            checkFee.setProjectGroupCode(oldProjectGropNumber);
            List<FeeDO> feeDOList = feeService.getFeePage(checkFee).getList();
            // 找出项目名下的所有计费详情记录
            FeeDetailPageReqVO checkFeeDetail = new FeeDetailPageReqVO();
            checkFeeDetail.setProjectCode(projectDO.getProjectCode());
            checkFeeDetail.setGroupCode(oldProjectGropNumber);
            List<FeeDetailDO> feeDetailDOList = feeDetailService.getFeeDetailPage(checkFeeDetail).getList();
            // 找出项目名下的所有开票记录
            InvMainPageReqVO checkInvMain = new InvMainPageReqVO();
            checkInvMain.setProjectCode(projectDO.getProjectCode());
            checkInvMain.setProjectGroupCode(oldProjectGropNumber);
            List<InvMainDO> invMainDOList = invMainService.getInvMainPage(checkInvMain).getList();

            // 修改项目记录的项目组编号
            projectDO.setProjectGroupCode(projectGropNumber);
            String projectCode = projectDO.getProjectCode();
            // 修改项目记录的项目编号，使用项目组编号直接替换掉项目编号的前8位
            String newProjectCode = projectGropNumber + projectCode.substring(8, 10);
            projectDO.setProjectCode(newProjectCode);
            // 将ProjectDO转化成ProjectSaveReqVO
            ProjectSaveReqVO projectSaveReqVO = BeanUtils.toBean(projectDO, ProjectSaveReqVO.class);
            try {
                projectService.updateProject(projectSaveReqVO);
            } catch (Exception e) {
                error("修改对应项目记录失败");
            }

            // 修改合同记录的信息
            for (ContractDO contractDO : contractDOList) {
                // 修改记录的项目组编号和项目编号
                contractDO.setProjectGroupCode(projectGropNumber);
                contractDO.setProjectCode(newProjectCode);
                // 修改合同编号
                System.out.println("修改前的编号是" + contractDO.getContractCode());
                String newContractNumber = contractDO.getContractCode().charAt(0) + projectGropNumber.substring(1, 5) + contractDO.getContractCode().substring(5);
                // 展示新编号
                System.out.println("修改后的编号是" + newContractNumber);
                // 回存新编号
                contractDO.setContractCode(newContractNumber);
                // 将ContractDO转化成ContractSaveReqVO
                ContractSaveReqVO contractSaveReqVO = BeanUtils.toBean(contractDO, ContractSaveReqVO.class);
                // 保存上述的修改结果
                try {
                    contractService.updateContract(contractSaveReqVO);
                } catch (Exception e) {
                    error("修改对应合同记录失败");
                }
            }
            // 修改计费记录的信息
            for (FeeDO feeDO : feeDOList) {
                // 修改记录的项目组编号和项目编号
                feeDO.setProjectGroupCode(projectGropNumber);
                feeDO.setProjectCode(newProjectCode);
                // 将FeeDO转化成FeeSaveReqVO
                FeeSaveReqVO feeSaveReqVO = BeanUtils.toBean(feeDO, FeeSaveReqVO.class);
                // 保存上述的修改结果
                try {
                    feeService.updateFee(feeSaveReqVO);
                } catch (Exception e) {
                    error("修改对应计费记录失败");
                }
            }
            // 修改计费详情记录的信息
            for (FeeDetailDO feeDetailDO : feeDetailDOList) {
                // 修改记录的项目组编号和项目编号
                feeDetailDO.setGroupCode(projectGropNumber);
                feeDetailDO.setProjectCode(newProjectCode);
                // 将FeeDetailDO转化成FeeDetailSaveReqVO
                FeeDetailSaveReqVO feeDetailSaveReqVO = BeanUtils.toBean(feeDetailDO, FeeDetailSaveReqVO.class);
                // 保存上述的修改结果
                try {
                    feeDetailService.updateFeeDetail(feeDetailSaveReqVO);
                } catch (Exception e) {
                    error("修改对应计费详情记录失败");
                }
            }
            // 修改开票记录的信息
            for (InvMainDO invMainDO : invMainDOList) {
                // 修改记录的项目组编号和项目编号
                invMainDO.setProjectGroupCode(projectGropNumber);
                invMainDO.setProjectCode(newProjectCode);
                // 将InvMainDO转化成InvMainSaveReqVO
                InvMainSaveReqVO invMainSaveReqVO = BeanUtils.toBean(invMainDO, InvMainSaveReqVO.class);
                // 保存上述的修改结果
                try {
                    invMainService.updateInvMain(invMainSaveReqVO);
                } catch (Exception e) {
                    error("修改对应开票记录失败");
                }
            }
        }
        // 返回成功讯号
        return success(null);
    }

    @GetMapping("/selectByYear")
    @Operation(summary = "查询指定年份的记录")
    @Parameter(name = "selectByYear", description = "根据项目组年份筛选出对应的项目记录", required = true)
    public CommonResult<PageResult<ProjectGropRespVO>> selectProjectGropListByYear(@RequestParam(value = "year") String year) {
        System.out.println("指定年份是" + year);
        ProjectGropPageReqVO checkGrop = new ProjectGropPageReqVO();
        // 制作字符串，要求形式是"G{year}"开头
        String projectGropNumber = "G" + year;
        checkGrop.setProjectGropNumber(projectGropNumber);
        // 获取项目组数据
        PageResult<ProjectGropDO> pageResult = projectGropService.getProjectGropPage(checkGrop);
        // 将项目组数据转换为项目组响应对象，后者增加了一些字段
        PageResult<ProjectGropRespVO> bean = BeanUtils.toBean(pageResult, ProjectGropRespVO.class);

        // 设置创建者昵称
        setCreatorNicknames(bean.getList());

        // 返回结果
        return success(bean);
    }


//    @GetMapping("/count-manager-empty")
//    @Operation(summary = "查询项目经理为空的项目组数量")
//    public CommonResult<Integer> countProjectGropWithEmptyManager() {
//        Integer count = myMapper.selectCountManagerIsNull();
//        return success(count);
//    }

//    @GetMapping("/getIfAcceptedAndHandlerFromList")
//    @Operation(summary = "获取指定项目组编号的项目验收情况和经办人情况")
//    @Parameter(name = "getIfAcceptedAndHandlerFromList", description = "根据项目组编号筛选出对应的项目验收情况和经办人情况", required = true)
//    public

    @PostMapping("/updateContractYear/{changedYear}/{projectGropNumber}")
    @Operation(summary = "根据提供的年份修改合同记录的编号")
    @Parameter(name = "changedYear", description = "根据提供的年份修改合同记录的编号", required = true)
    public CommonResult<Boolean> updateContractYear(@PathVariable("changedYear") String changedYear, @PathVariable("projectGropNumber") String projectGropNumber) {
        // 展示信息
        System.out.println("修改成的年份是" + changedYear + "，项目组编号是" + projectGropNumber);
        // 查询出该项目组编号下的所有合同信息
        ContractPageReqVO contractPageReqVO = new ContractPageReqVO();
        // 导入项目组编号
        contractPageReqVO.setProjectGroupCode(projectGropNumber);
        // 获得合同列表
        List<ContractDO> contractDOList = contractService.getContractPage(contractPageReqVO).getList();
        // 展示合同列表
        System.out.println("该项目组编号下的合同列表是");
        for (ContractDO contractDO : contractDOList) {
            System.out.println(contractDO);
        }
        // 对每一个合同编号进行修改
        for (ContractDO contractDO : contractDOList) {
            // 如果编号是"C2025196R1"，将其2025修改掉
            String newContractNumber = contractDO.getContractCode().charAt(0) + changedYear + contractDO.getContractCode().substring(5);
            // 展示新编号
            System.out.println("修改后的编号是" + newContractNumber);
            // 回存新编号
            contractDO.setContractCode(newContractNumber);
        }
        return success(null);
    }

    /**
     * 为项目组列表设置创建者昵称
     *
     * @param projectGropList 项目组列表
     */
    private void setCreatorNicknames(List<ProjectGropRespVO> projectGropList) {
        if (projectGropList == null || projectGropList.isEmpty()) {
            return;
        }

        // 批量查询创建者昵称
        Set<String> creatorIds = projectGropList.stream()
                .map(ProjectGropRespVO::getCreator)
                .filter(Objects::nonNull)
                .filter(s -> !s.isEmpty())  // 再过滤空字符串
                .collect(Collectors.toSet());

        if (!creatorIds.isEmpty()) {
            // 将String类型的ID转换为Long类型
            List<Long> userIds = creatorIds.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            List<AdminUserDO> users = adminUserService.getUserList(userIds);
            Map<String, String> creatorNicknameMap = users.stream()
                    .collect(Collectors.toMap(
                            user -> user.getId().toString(),
                            AdminUserDO::getNickname
                    ));

            // 设置创建者昵称
            for (ProjectGropRespVO projectGropRespVO : projectGropList) {
                if (projectGropRespVO.getCreator() != null) {
                    String nickname = creatorNicknameMap.get(projectGropRespVO.getCreator());
                    projectGropRespVO.setCreatorNickname(nickname);
                }
            }
        }
    }

    /**
     * 获取项目组的绩效信息
     * @param projectGroupIds 项目组ID列表
     * @return 项目组ID到绩效信息的映射
     */
    private Map<Long, String> getPerformanceInfoForProjectGroups(List<Long> projectGroupIds) {
        Map<Long, String> performanceInfoMap = new HashMap<>();

        for (Long projectGroupId : projectGroupIds) {
            try {
                // 根据项目组ID获取项目人员列表
                List<ProjectPersonDO> projectPersonList = projectPersonService.getProjectPersonListByProjectGroupId(projectGroupId.toString());

                if (projectPersonList != null && !projectPersonList.isEmpty()) {
                    // 构建绩效信息字符串
                    StringBuilder performanceInfo = new StringBuilder();

                    // 统计不同时间和确认状态的信息
                    Map<String, Long> timeConfirmStats = projectPersonList.stream()
                            .filter(person -> person.getTime() != null && !person.getTime().trim().isEmpty())
                            .collect(Collectors.groupingBy(
                                    person -> {
                                        String time = person.getTime();
                                        String confirm = person.getManagerConfirm() != null ? person.getManagerConfirm() : "未确认";
                                        return time + "(" + confirm + ")";
                                    },
                                    Collectors.counting()
                            ));

                    if (!timeConfirmStats.isEmpty()) {
                        performanceInfo.append("绩效统计: ");
                        timeConfirmStats.entrySet().stream()
                                .sorted(Map.Entry.<String, Long>comparingByKey())
                                .forEach(entry -> {
                                    performanceInfo.append(entry.getKey())
                                            .append(": ")
                                            .append(entry.getValue())
                                            .append("人; ");
                                });

                        // 移除最后的分号和空格
                        if (performanceInfo.length() > 2) {
                            performanceInfo.setLength(performanceInfo.length() - 2);
                        }
                    } else {
                        performanceInfo.append("暂无绩效记录");
                    }

                    performanceInfoMap.put(projectGroupId, performanceInfo.toString());
                } else {
                    performanceInfoMap.put(projectGroupId, "暂无项目成员");
                }
            } catch (Exception e) {
                // 如果查询出错，记录错误信息
                performanceInfoMap.put(projectGroupId, "绩效信息查询失败");
            }
        }

        return performanceInfoMap;
    }
}
