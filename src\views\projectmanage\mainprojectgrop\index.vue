<!-- 项目管理-项目组 -->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="90px"
    >

      <el-form-item label="项目组名称" prop="projectGropName">
        <el-input
          v-model="queryParams.projectGropName"
          placeholder="请输入项目组名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目组编号" prop="projectGropNumber">
        <el-input
          v-model="queryParams.projectGropNumber"
          placeholder="请输入项目组编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="部门" prop="managingDepartment">
        <el-select
          v-model="queryParams.managingDepartment"
          placeholder="请选择部门"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MANAGING_DEPARTMENT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目经理" prop="projectManager">
        <el-select
          v-model="queryParams.projectManager"
          placeholder="请输入项目经理"
          clearable
          filterable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getFilteredDictOptions()"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目成员" prop="projectMemberName">
        <el-input
          v-model="queryParams.projectMemberName"
          placeholder="请输入项目成员姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>




<!--      <el-form-item label="备注" prop="remarks">-->
<!--        <el-input-->
<!--          v-model="queryParams.remarks"-->
<!--          placeholder="请输入备注"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="项目组年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          value-format="YYYY"
          class="!w-240px"
          clearable
          @keyup.enter="handleQuery"
          />
      </el-form-item>
      <el-form-item >
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['projectmanage:project-grop:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['projectmanage:project-grop:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>

<!--        <el-button type="primary" plain @click="filterEmptyManager" style="margin-left: 950px;">-->
<!--          未选任项目经理-->
<!--          <el-badge-->
<!--            :value="emptyManagerCount"-->
<!--            :max="99"-->
<!--            class="item"-->
<!--          />-->
<!--        </el-button>-->
        <el-badge :value="emptyManagerCount" :max="9999" class="item">
          <el-button type="primary" plain @click="filterEmptyManager2" style="margin-left: 10px;" >
            未选任项目经理
          </el-button>
        </el-badge>

      </el-form-item>
    </el-form>
  </ContentWrap>
<!--  <el-divider content-position="left">项目组</el-divider>-->
  <!-- 列表 -->
  <ContentWrap>
    <el-button @click="mergeProjectGrop" type="primary" style="margin-top: 10px;margin-left: 10px;" :disabled="ifDisabled">合并项目组</el-button>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @row-click="handleRowClick" @row-dblclick="groupRowDblclick" highlight-current-row   @selection-change="handleSelectionChange" >
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column type="selection" width="55"/>
      <el-table-column label="编号" align="center" prop="projectGropNumber" sortable width="100px"/>
      <el-table-column label="项目组名称" align="center" width="350px" :show-overflow-tooltip="false">
        <template #default="scope">
          <el-tooltip
            effect="dark"
            placement="top"
            :disabled="!scope.row.receivableProjectsTooltip"
            :content="scope.row.receivableProjectsTooltip || ''"
            popper-class="receivable-projects-tooltip"
          >
            <span
              class="project-name-cell"
              @mouseenter="loadReceivableProjects(scope.row)"
              style="cursor: pointer;"
            >
              {{ scope.row.projectGropName }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="项目状态" align="center" prop="statusText" width="100px">
        <template #default="scope">
          <el-tag :type="scope.row.statusText === '已验收' ? 'success' : 'warning'">
            {{ scope.row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="类型" prop="type"  width="150">
        <template #default="scope">
          <dict-tag :type="'groupType'" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="收款合同额" align="center" width="150">
        <template #default="scope">
          <span :class="{'red-text': scope.row.receivable > 50000}">{{ formatCurrency(scope.row.receivable) || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目经理" align="center" prop="projectManager" width="130px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="oarouter(scope.row.flowId)"
            v-if="scope.row.flowId"
          >
            {{ scope.row.projectManager }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="合同年份" align="center" prop="earliestContractYear" width="100px"/>
      <el-table-column label="团队人数" align="center" prop="personCount" width="100px"/>
      <el-table-column label="工期(天)" align="center" prop="workDay" width="80px"/>
      <el-table-column label="主办部门" align="center"  prop="department"  width="110px"/>
<!--      <el-table-column label="项目进度" align="center"  prop="projectGroupProgress"  width="80px"/>-->
<!--      <el-table-column label="超五万" align="center"  prop="overFiveWanCount"  width="110px"/>-->
      <!--      <el-table-column label="收款金额" align="center" prop="receivable" />-->
<!--      <el-table-column label="已收款金额" align="center" prop="receivedAmount" />-->
<!--      <el-table-column label="付款金额" align="center" prop="payable" />-->
<!--      <el-table-column label="已付款金额" align="center" prop="paidAmount" />-->
<!--      <el-table-column label="利润" align="center" prop="profit" />-->
<!--      <el-table-column label="备注" align="center" prop="remarks" />-->
<!--      <el-table-column-->
<!--        label="创建时间"-->
<!--        align="center"-->
<!--        prop="createTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="180px"-->
<!--      />-->
<!--      <el-table-column label="收款合同额" align="center" width="160">-->
<!--        <template #default="scope">-->
<!--          <span class="red-text">{{ formatCurrency(scope.row.receivable) || '0.00' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="开票进度" align="center" width="90px">-->
<!--        <template #default="scope">-->
<!--          <span class="red-text">{{ calculateInvPercentage(scope.row) }}%</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="收款进度" align="center" width="90px">-->
<!--        <template #default="scope">-->
<!--          <span class="red-text">{{ calculateReceivablePercentage(scope.row) }}%</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="付款合同额" align="center" width="160px">-->
<!--        <template #default="scope">-->
<!--          <span class="green-text">{{ formatCurrency(scope.row.payable) || '0.00' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="收票进度" align="center" width="90px" >-->
<!--        <template #default="scope">-->
<!--          <span class="green-text">{{ calculateInvPayPercentage(scope.row) }}%</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="付款进度" align="center" width="90px">-->
<!--        <template #default="scope">-->
<!--          <span class="green-text">{{ calculatePayablePercentage(scope.row) }}%</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="性质" align="center" width="80px">-->
<!--        <template #default="scope">-->
<!--          <el-tag v-if="scope.row.nature" :type="scope.row.nature === '运维服务' ? 'success' : 'primary'">-->
<!--            {{ scope.row.nature }}-->
<!--          </el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="重要性" align="center" width="120px">-->
<!--        <template #default="scope">-->
<!--          <el-tag v-if="scope.row.importance" :type="scope.row.importance === '一般项目' ? 'success' : 'primary'">-->
<!--            {{ scope.row.importance }}-->
<!--          </el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="项目系数" align="center" width="120px">-->
<!--        <template #default="scope">-->
<!--          <span>{{ scope.row.coefficient || '-' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="绩效信息" align="center" width="200px" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ scope.row.performanceInfo || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter1"
        width="160px"
      />
      <el-table-column label="操作" align="center" min-width="110px" fixed="right" >
        <template #default="scope">
        <!-- 全览按钮 - 不再区分权限 -->
        <el-button
          link
          type="primary"
          @click="goToDetail(scope.row)"

        >
          详情
        </el-button>


        <el-button
          link
          type="danger"
          @click="handleDelete(scope.row.id)"
          v-hasPermi="['projectmanage:project-grop:delete']"
        >
          删除
        </el-button>

        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 项目表（隐藏） -->
  <!-- <el-divider content-position="left">项目</el-divider>
  <ContentWrap>
    <ProjectTable ref="projectTableRef" @row-click="handleProjectRowClick" />
  </ContentWrap>
  <el-divider content-position="left">项目资金情况</el-divider>
  <ContentWrap>
    <FeeTable ref="feeTableRef" :show-operations="false" />
  </ContentWrap> -->

  <!-- 表单弹窗：添加/修改 -->
  <ProjectGropForm ref="formRef" @success="getList" />
  <el-dialog v-model="mergeDialogVisible" title="合并项目组" width="40%" v-loading="mergeLoading">
    <!--<el-form :model=""-->
    <el-divider style="margin-top: 0"/>
    <el-form :model="mergeForm">
      <el-row>
        <el-col :span="10"  class="trySet">
          <el-form-item label="项目组名称"  label-width="180px">
            <el-input v-model="mergeForm.projectGropName" placeholder="请输入新的项目组名称" clearable class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="4" style="position: relative;">
          <el-button type="primary" @click="submitMergeProjectGrop" style="position: absolute; right: 90px;">确认合并</el-button>
          <el-button type="info" @click="mergeDialogVisible = false"  style="position: absolute; right: 20px;">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts" name="MainProjectGrop">
import {dateFormatter, dateFormatter1} from '@/utils/formatTime'
import download from '@/utils/download'
import { ProjectGropApi, ProjectGropVO } from '@/api/projectmanage/projectgrop'
import ProjectGropForm from './ProjectGropForm.vue'
import {ElMessage, ElOption} from "element-plus";
import { useRouter, useRoute } from 'vue-router'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache';
import { useUserStoreWithOut } from '@/store/modules/user';
import {ProjectManagerApprovalApi} from "@/api/projectmanage/projectmanagerapproval";
import {pinyin} from "pinyin";

/** 项目组 列表 */
/*defineOptions({ name: 'ProjectGrop' })*/
defineOptions({ name: 'MainProjectGrop' })
const router = useRouter()
const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const list = ref<ProjectGropVO[]>([]) // 列表的数据
const list2 = ref<ProjectGropVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showBadge = ref(false)
const isFilteringEmptyManager = ref(false)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  id: undefined,
  projectGropNumber: undefined,
  projectGropName: undefined,
  receivable: undefined,
  receivedAmount: undefined,
  payable: undefined,
  paidAmount: undefined,
  invAmount: undefined,
  invPayAmount: undefined,
  profit: undefined,
  remarks: undefined,
  type: [],
  createTime: [],
  managingDepartment: undefined,
  projectManager: undefined,
  year : undefined,
  projectMemberName: undefined,
})

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const emptyManagerCount = ref(0) // 未选任项目经理的数量

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    let data;
    if (isFilteringEmptyManager.value){
      data = await ProjectGropApi.getProjectGropPageWithputManager(queryParams)//点击未选任项目经理的接口
    }
     else if (queryParams.managingDepartment) {
      // 如果有部门参数，调用部门搜索接口
      data = await ProjectGropApi.getProjectGropByDepartment(
        queryParams.managingDepartment,
        queryParams.pageNo,
        queryParams.pageSize,
      )
    } else if(queryParams.year) {
      // 如果有年份参数，调用部门搜索接口
      console.log("年份参数：",queryParams.year)
      data = await ProjectGropApi.selectListByYear(
        queryParams.year,
        queryParams.pageNo,
        queryParams.pageSize,
      )
      console.log("年份查询结果：",data)
    } else {
      // 否则调用常规分页接口
      data = await ProjectGropApi.getProjectGropPage(queryParams)
    }
    console.log("查询结果：",data)
    list.value = data.list
    console.log("8848:",list.value)
    total.value = data.total
  } finally {
    loading.value = false
    mergeDialogVisible.value = false
  }
}



const filterEmptyManager = async () => {
   //emptyManagerCount.value = await ProjectGropApi.getEmptyManagerCount();
  try {
    // 传递当前查询条件
    const filterParams = {
      ...queryParams,
      // managingDepartment: queryParams.managingDepartment || undefined,
      // year: queryParams.year || undefined,
      // projectGropName: queryParams.projectGropName || undefined,
      // projectGropNumber: queryParams.projectGropNumber || undefined,
      pageNo: undefined,
      pageSize: undefined
    }
    console.log("未选项目经理查询条件：",filterParams)
    const data = await ProjectGropApi.getEmptyManagerCount(filterParams)
    // emptyManagerCount.value = await ProjectGropApi.getEmptyManagerCount(filterParams)
    emptyManagerCount.value = data
    console.log("未选项目经理数量：",data)
  } catch (error) {
    console.error('统计失败', error)
  }
}

const filterEmptyManager2 = async () => {
  try {
    isFilteringEmptyManager.value = true;
    const pageData = await ProjectGropApi.getProjectGropPageWithputManager(queryParams);
    console.log("未选项目经理列表：",pageData)
    list.value =pageData.list;
    total.value = pageData.total;
    emptyManagerCount.value = pageData.total;
  } catch (error) {
    console.error('操作失败', error);
  }
}


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  isFilteringEmptyManager.value = false
  getList()
  filterEmptyManager()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  isFilteringEmptyManager.value = false
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}


/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProjectGropApi.deleteProjectGrop(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
    message.error(t('出现错误'))
  } finally {

  }
}



/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProjectGropApi.exportProjectGrop(queryParams)
    download.excel(data, '项目组.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 合并项目组弹窗显示
const mergeDialogVisible = ref(false)
// 合并项目组弹窗加载
const mergeLoading = ref(false)
// 合并项目组弹窗禁用
const ifDisabled = ref(true)
// 合并项目组弹窗参数
const mergeForm = ref({
  projectGropName: '',
  projectGropNumber: '',
  projectGropId: '',
  projectCode: '',
})
// 合并项目组方法
const mergeProjectGrop = () => {
  mergeDialogVisible.value = true
}
// 选中项目组记录数据
const selectRow = ref<ProjectGropVO | null>()
// 选中项目组记录行的方法
const handleSelectionChange = (selection) => {
  selectRow.value = selection
  console.log(selectRow.value)
  if (selection.length > 1) {
    ifDisabled.value = false
  } else {
    ifDisabled.value = true
  }
}
// 合并修改提交按钮方法
const submitMergeProjectGrop = async() => {
  // 打开加载层
  mergeLoading.value = true
  try {
    // 确保传递的是数组
    await ProjectGropApi.mergeProjectGroup(Array.isArray(selectRow.value) ? selectRow.value : [], mergeForm.value.projectGropName)
  } catch (error) {
    // message.error("合并项目组失败")
  } finally {
    // 关闭加载层
    mergeLoading.value = false
    // 刷新列表
    getList()
  }
}

/** 初始化 **/
onMounted(async () => {
  console.log('项目组列表初始化',queryParams)
  await getList()
  await filterEmptyManager()
})

/** 处理项目组行点击事件 */
const projectTableRef = ref()
const feeTableRef = ref()

// 项目组双击去项目组全览
const groupRowDblclick = (row) => {
  goToDetail(row);
}

const handleRowClick = (row) => {
  // 获取点击行的项目组编号
  const projectGroupCode = row.projectGropNumber
  // 调用子组件的方法刷新数据
  projectTableRef.value?.refreshByGroupCode(projectGroupCode)
  feeTableRef.value?.refreshByGroupCode(projectGroupCode)
}

/** 处理项目表行点击事件 */
const handleProjectRowClick = (row) => {
  // 获取点击行的项目编号
  const projectCode = row.projectCode
  // 调用FeeTable的方法刷新数据
  feeTableRef.value?.refreshByProjectCode(projectCode)
}

const calculateReceivablePercentage = (row) => {
  if (!row.receivable || row.receivable === 0) return '0.00'
  const percentage = (row.receivedAmount / row.receivable) * 100
  return percentage.toFixed(2)
}

const calculateInvPercentage = (row) => {
  if (!row.receivable || row.receivable === 0) return '0.00'
  const percentage = (row.invAmount || 0) / row.receivable * 100
  return percentage.toFixed(2)
}

const calculateInvPayPercentage = (row) => {
  if (!row.payable || row.payable === 0) return '0.00'
  const percentage = (row.invPayAmount || 0) / row.payable * 100
  return percentage.toFixed(2)
}

// 缓存已加载的收款项目信息，避免重复请求
const receivableProjectsCache = new Map()

/** 加载收款项目信息 */
const loadReceivableProjects = async (row: ProjectGropVO) => {
  // 如果已经有tooltip内容，直接返回
  if (row.receivableProjectsTooltip) {
    return
  }

  // 检查缓存
  if (receivableProjectsCache.has(row.projectGropNumber)) {
    row.receivableProjectsTooltip = receivableProjectsCache.get(row.projectGropNumber)
    return
  }

  try {
    // 调用API获取收款项目列表
    const receivableProjects = await ProjectGropApi.getReceivableProjects(row.projectGropNumber)

    if (receivableProjects && receivableProjects.length > 0) {
      // 提取项目名称并组合成tooltip内容
      const projectNames = receivableProjects.map(project => project.projectName).join('\n')
      const tooltipContent = `收款项目：\n${projectNames}`

      // 设置tooltip内容
      row.receivableProjectsTooltip = tooltipContent

      // 缓存结果
      receivableProjectsCache.set(row.projectGropNumber, tooltipContent)
    } else {
      const noProjectsContent = '暂无收款项目'
      row.receivableProjectsTooltip = noProjectsContent
      receivableProjectsCache.set(row.projectGropNumber, noProjectsContent)
    }
  } catch (error) {
    console.error('获取收款项目失败:', error)
    const errorContent = '获取收款项目失败'
    row.receivableProjectsTooltip = errorContent
    receivableProjectsCache.set(row.projectGropNumber, errorContent)
  }
}

const calculatePayablePercentage = (row) => {
  if (!row.payable || row.payable === 0) return '0.00'
  const percentage = (row.paidAmount / row.payable) * 100
  return percentage.toFixed(2)
}

// 跳转到项目组详情页面
const goToProjectDetail = (row) => {
  router.push(`/projectmanage/projectgrop/${row.projectGropNumber}`)
}
// 跳转到全览页面
const goToDetail = (row) => {
  console.log('跳转到全览页面，传递参数：', row)
  router.push({
    name: 'projectOverview',
    query: {
      id: row.id.toString(),
      projectGropNumber: row.projectGropNumber,
      projectGropName: row.projectGropName
    }
  })
}
// 金额显示规范
const formatCurrency = (amount: number | undefined): string => {
  if (amount === undefined|| amount === null || amount === 0) {
    return '¥ -'
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

// 初始化用户相关工具
const { wsCache } = useCache()
const userStore = useUserStoreWithOut()

// 获取当前用户部门ID
const getCurrentUserDeptId = () => {
  const userInfo = wsCache.get(CACHE_KEY.USER);
  console.log("当前登录用户部门ID：",userInfo?.user?.deptId)
  return userInfo?.user?.deptId || null;

}

// 判断是否为超级管理员
const isAdmin = () => {
  const userInfo = wsCache.get(CACHE_KEY.USER);
  const currentUsername = userInfo?.user?.nickname || '';
  return currentUsername === "超级管理员";
}

// 判断是否为公司领导（部门ID=197）
const isLeader = () => {
  return getCurrentUserDeptId() === 197;
}

// 判断是否为业务部（部门ID=198）
const isBusinessDept = () => {
  return getCurrentUserDeptId() === 198;
}



// 判断是否为软件开发部（部门ID=199或201）
const isDevelopmentDept = () => {
  const deptId = getCurrentUserDeptId();
  return deptId ==199 || deptId == 201;
}

// 超级管理员、公司领导或软件开发部可以看到全览按钮
const isAdminOrLeader = () => {
  return isAdmin() || isLeader() ;
}

// 超级管理员、公司领导和业务部可以编辑
const hasEditPermission = () => {
  return isAdmin() || isLeader() || isBusinessDept();
}

// 处理详情按钮点击，根据不同部门跳转到不同页面
const handleDetailClick = (row) => {
  if (isDevelopmentDept() && !isAdmin() && !isLeader()) {
    // 软件开发部人员跳转到全览页面
    goToDetail(row);
  } else {
    // 超级管理员、公司领导和业务部人员跳转到详情页面
    goToProjectDetail(row);
  }
}

// 获取当前用户角色
const userRole = computed(() => {
  const userInfo = wsCache.get(CACHE_KEY.USER);
  return userInfo?.user?.deptId || null;
});
console.log('当前用户角色1：', userRole.value)
// 根据角色过滤字典项
const getFilteredDictOptions = () => {
  const deptIdStr = String(userRole.value);
  // 公司领导看到所有四个部门字典的合集
  if (deptIdStr === '197' || deptIdStr === '115') {
    return [
      ...getStrDictOptions('rjyb'),
      ...getStrDictOptions('rjeb'),
      ...getStrDictOptions('xtjc'),
      ...getStrDictOptions('txfw')
    ];
  }
  if (deptIdStr === '199') {
    return getStrDictOptions('rjyb');
  } else if (deptIdStr === '201') {
    return getStrDictOptions('rjeb');
  } else if (deptIdStr === '203') {
    return getStrDictOptions('xtjc');
  } else if (deptIdStr === '202') {
    return getStrDictOptions('txfw');
  }
  return [];
};

const oarouter = async (flowId: string) => {
  const nickname = await ProjectManagerApprovalApi.getName()
  console.log(nickname)
  const timeTick = Date.now().toString();//时间戳
  const name = nickname ;//获取当前登录用户的姓名
  console.log(name)
  const yonghu = pinyin(name, {style: pinyin.STYLE_NORMAL}).flat().join('');//获取当前登录用户的拼音
  console.log(yonghu)
  const data = await  ProjectManagerApprovalApi.oarouter({
    yonghu:yonghu,
    timeTick:timeTick
  })
  const signature = data          //获取的临时口令
  let encodedFlowId = encodeURIComponent(flowId);
  let nexturl = `/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${encodedFlowId}`
  const url=`https://oa.gzport.com/oa/api/auth/login_by_sso?sso=customer_sso&loginName=${yonghu}&tickTime=${timeTick}&signature=${signature}&next=`+escape(nexturl)
  console.log(url)
  window.open(url);
}

</script>

<style scoped>
.green-text {
  color: #67C23A;
  font-weight: bold;
}

.red-text {
  color: #F56C6C;
  font-weight: bold;
}

.trySet {
  display: flex;
  justify-content: flex-start;
}

.item {
  margin-bottom: 1px;
}

.project-name-cell {
  white-space: normal;
  word-break: break-all;
}
</style>



